<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use <PERSON><PERSON>\Sanctum\Http\Middleware\CheckAbilities;
use Lara<PERSON>\Sanctum\Http\Middleware\CheckForAnyAbility;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        health: '/up',
        web: __DIR__.'/../routes/web.php',
        api: [
            'v1' => __DIR__.'/../routes/api_v1.php',
        ],
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->statefulApi();
        $middleware->alias([
            'languages' => App\Http\Middleware\Languages::class,
            'admin' => App\Http\Middleware\AdminMiddleware::class,
            'abilities' => CheckAbilities::class,
            'ability' => CheckForAnyAbility::class,
            'check.role.subscription' => App\Http\Middleware\CheckRoleAndSubscription::class,
            'log.activity' => App\Http\Middleware\LogSystemActivity::class,
            'maintenance' => App\Http\Middleware\MaintenanceMode::class,
            'registration.enabled' => App\Http\Middleware\CheckRegistrationEnabled::class,
            'track.visits' => App\Http\Middleware\TrackPageVisits::class,
            'guest' => App\Http\Middleware\RedirectIfAuthenticated::class,
        ]);
        $middleware->appendToGroup('web', ['maintenance', 'languages', 'check.role.subscription', 'log.activity', 'track.visits']);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (\Exception $e, $request) {
            // معالجة أخطاء قاعدة البيانات المتعلقة بالمصادقة
            if (str_contains($e->getMessage(), 'foreign key constraint') && 
                str_contains($e->getMessage(), 'system_logs')) {
                
                \Log::error('Auth System Log Error: ' . $e->getMessage());
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => __('Authentication failed. Please try again.')
                    ], 422);
                }
                
                return redirect()->back()
                    ->withInput($request->except('password'))
                    ->withErrors(['email' => __('Authentication failed. Please try again.')]);
            }
        });
    })
    ->create();
