# دليل إعداد Paymob للدفع الإلكتروني

## المشكلة الحالية
يبدو أن بيانات الاعتماد المقدمة لـ Paymob غير صحيحة أو منتهية الصلاحية. API Key المقدم يبدو أنه JWT token منتهي الصلاحية.

## الحل المطلوب

### 1. الحصول على API Key جديد
1. سجل الدخول إلى لوحة Paymob: https://accept.paymob.com
2. اذهب إلى **Developers** > **API Keys**
3. أنشئ **API Key جديد** (ليس JWT token)
4. انسخ API Key الجديد

### 2. الحصول على Integration ID
1. في لوحة Paymob، اذهب إلى **Developers** > **Payment Integrations**
2. اختر التكامل الخاص بك
3. انسخ **Integration ID**

### 3. الحصول على HMAC Secret
1. في لوحة Paymob، اذهب إلى **Developers** > **Webhooks**
2. انسخ **HMAC Secret**

### 4. إنشاء Iframe
1. اذهب إلى **Developers** > **iFrames**
2. أنشئ iframe جديد أو استخدم موجود
3. انسخ **Iframe ID**

### 5. تحديث الإعدادات
1. اذهب إلى صفحة الإعدادات في النظام
2. اختر تبويب **Payment Settings**
3. فعل **Enable Paymob**
4. أدخل البيانات الجديدة:
   - **Paymob API Key**: API Key الجديد (ليس JWT token)
   - **Paymob Integration ID**: Integration ID
   - **Paymob Iframe ID**: Iframe ID
   - **Paymob HMAC Secret**: HMAC Secret
   - **Paymob Mode**: اختر Sandbox للاختبار أو Live للإنتاج

## ملاحظات مهمة
- تأكد من استخدام بيانات البيئة الصحيحة (Test/Live)
- API Key يجب أن يكون نشط وغير منتهي الصلاحية
- تأكد من أن Integration مفعل في لوحة Paymob
- HMAC Secret مطلوب للتحقق من صحة المدفوعات

## اختبار الإعداد
بعد تحديث البيانات، جرب إنشاء اشتراك تجريبي للتأكد من عمل النظام بشكل صحيح.