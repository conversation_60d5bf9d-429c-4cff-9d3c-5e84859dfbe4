<?php

use App\Http\Controllers\WEB\About\Index as AboutController;
use App\Http\Controllers\WEB\Auth\RegisterForm;
use App\Http\Controllers\WEB\Categories\Index as CategoriesController;
use App\Http\Controllers\WEB\Collections\Create as NewCollectionsController;
use App\Http\Controllers\WEB\Collections\Index as CollectionsController;
use App\Http\Controllers\WEB\Collections\Update as UpdateCollectionsController;
use App\Http\Controllers\WEB\Dashboard\Index as DashboardController;
use App\Http\Controllers\WEB\Design\Index as DesignController;
use App\Http\Controllers\WEB\Education\Index as EducationController;
use App\Http\Controllers\WEB\Experience\Index as ExperienceController;
use App\Http\Controllers\WEB\Features\Index as FeaturesController;
use App\Http\Controllers\WEB\Front\V1\Index as FrontController;
use App\Http\Controllers\WEB\Front\V1\LinkTree as FrontLinkTreeController;
use App\Http\Controllers\WEB\Front\V1\Pages as FrontPagesController;
use App\Http\Controllers\WEB\Front\V1\Profile as ProfileController;
use App\Http\Controllers\WEB\Front\V1\ProfessionalProfile;
use App\Http\Controllers\WEB\Profile\Index as ProfilesController;
use App\Http\Controllers\WEB\Profile\Create as NewProfileController;
use App\Http\Controllers\WEB\Profile\Update as UpdateProfileController;
use App\Http\Controllers\WEB\Front\V1\Projects as ProjectsController;
use App\Http\Controllers\WEB\Home\Create as NewHomeController;
use App\Http\Controllers\WEB\Home\Index as HomeController;
use App\Http\Controllers\WEB\Home\Update as UpdateHomeController;
use App\Http\Controllers\WEB\LandingPage\Design as LandingController;
use App\Http\Controllers\WEB\Languages\Index as LanguagesController;
use App\Http\Controllers\WEB\LinkTree\Create as NewLinkTreeController;
use App\Http\Controllers\WEB\LinkTree\Index as LinkTreeController;
use App\Http\Controllers\WEB\LinkTree\Update as UpdateLinkTreeController;
use App\Http\Controllers\WEB\Messages\Index as MessageController;
use App\Http\Controllers\WEB\Pages\Create as CreatePageController;
use App\Http\Controllers\WEB\Pages\Index as PagesController;
use App\Http\Controllers\WEB\Pages\Show as ShowPageController;
use App\Http\Controllers\WEB\Pages\Update as UpdatePageController;
use App\Http\Controllers\WEB\Payments\AdminPaymentHistory;
use App\Http\Controllers\WEB\Payments\PaymentHistory;
use App\Http\Controllers\WEB\Plans\Index as PlansController;
use App\Http\Controllers\WEB\Portfolio\Index as PortfolioController;
use App\Http\Controllers\WEB\Roles\Index as RolesController;
use App\Http\Controllers\WEB\Settings\Index as SettingsController;
use App\Http\Controllers\WEB\Skills\Index as SkillsController;
use App\Http\Controllers\WEB\Social\Index as SocialsController;
use App\Http\Controllers\WEB\SystemLogs\Index as SystemLogsController;
use App\Http\Controllers\WEB\SystemLogs\Show as SystemLogsShowController;
use App\Http\Controllers\WEB\Themes\Index as ThemesController;
use App\Http\Controllers\WEB\Users\Index as UserController;
use App\Http\Controllers\WEB\Users\Profile;
use App\Http\Controllers\WEB\Webhooks\PaymobWebhook;
use App\Http\Controllers\WEB\Webhooks\PayPalWebhook;
use App\Http\Controllers\WEB\Webhooks\StripeWebhook;
use App\Models\About;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Design;
use App\Models\Education;
use App\Models\Experience;
use App\Models\Feature;
use App\Models\Language;
use App\Models\Message;
use App\Models\Page;
use App\Models\Plan;
use App\Models\Portfolio as PortfolioModel;
use App\Models\Role;
use App\Models\Skill;
use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware([
    'auth:sanctum', 'check.role.subscription',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', DashboardController::class)->name('dashboard');
    Route::get('/payment/success', [\App\Http\Controllers\WEB\Subscriptions\Subscribe::class, 'handlePaymentSuccess'])->name('payment.success');
    Route::get('/payment/cancel', [\App\Http\Controllers\WEB\Subscriptions\Subscribe::class, 'handlePaymentCancel'])->name('payment.cancel');

    Route::get('/profile', Profile::class)->name('profile');
    Route::get('/users', UserController::class)->name('users.index')->can('viewAny', User::class);
    Route::get('/roles', RolesController::class)->name('roles.index')->can('viewAny', Role::class);
    Route::get('/languages', LanguagesController::class)->name('languages.index')->can('viewAny', Role::class);
    Route::get('/about', AboutController::class)->name('about.index'); // ->can('viewAny', About::class);
    Route::get('/features', FeaturesController::class)->name('features.index')->can('viewAny', Feature::class);
    Route::get('/plans', PlansController::class)->name('plans.index')->can('viewAny', Plan::class);
    // Skills
    Route::get('/skills', SkillsController::class)->name('skills.index')->can('viewAny', Skill::class);
    // Education
    Route::get('/education', EducationController::class)->name('education.index')->can('viewAny', Education::class);
    // Experience
    Route::get('/experience', ExperienceController::class)->name('experience.index')->can('viewAny', Experience::class);
    // Categories
    Route::get('/categories', CategoriesController::class)->name('categories.index')->can('viewAny', Category::class);
    // Portfolio
    Route::get('/portfolio', PortfolioController::class)->name('portfolio.index')->can('viewAny', PortfolioModel::class);
    // Design
    Route::get('/designs', DesignController::class)->name('design.index')->can('viewAny', Design::class);
    // Collections
    Route::get('/collections', CollectionsController::class)->name('collections.index')->can('viewAny', Collection::class);
    Route::get('/collections/new', NewCollectionsController::class)->name('collections.create')->can('viewAny', Collection::class);
    Route::get('/collections/edit/{id}', UpdateCollectionsController::class)->name('collections.update')->can('viewAny', Collection::class);
    // Profiles
    Route::get('/profiles', ProfilesController::class)->name('profiles.index')->can('viewAny', \App\Models\Profile::class);
    Route::get('/profiles/new', NewProfileController::class)->name('profiles.create')->can('create', \App\Models\Profile::class);
    Route::get('/profiles/edit/{profile}', UpdateProfileController::class)->name('profiles.update')->can('update', 'profile');
    // Socials
    Route::get('/socials', SocialsController::class)->name('socials.index'); // ->can('viewAny', \App\Models\Social::class);
    Route::get('/linktree', LinkTreeController::class)->name('linktree.index'); // ->can('viewAny', \App\Models\Social::class);
    Route::get('/linktree/new', NewLinkTreeController::class)->name('linktree.create'); // ->can('viewAny', \App\Models\Social::class);
    Route::get('/linktree/edit/{id}', UpdateLinkTreeController::class)->name('linktree.update'); // ->can('viewAny', \App\Models\Social::class);

    // Pages Management
    Route::get('/pages', PagesController::class)->name('pages.index')->can('viewAny', Page::class);
    Route::get('/pages/new', CreatePageController::class)->name('pages.create')->can('create', Page::class);
    Route::get('/pages/{page}', ShowPageController::class)->name('pages.show');
    Route::get('/pages/edit/{page}', UpdatePageController::class)->name('pages.update');

    // Home Page Management
    Route::get('/home', HomeController::class)->name('home.index'); // ->can('viewAny', \App\Models\Home::class);
    Route::get('/home/<USER>', NewHomeController::class)->name('home.create'); // ->can('viewAny', \App\Models\Home::class);
    Route::get('/home/<USER>/{id}', UpdateHomeController::class)->name('home.update'); // ->can('viewAny', \App\Models\Home::class);
    // Messages
    Route::get('/messages', MessageController::class)->name('messages.index')->can('viewAny', Message::class);
    // System Logs
    Route::get('/system-logs', SystemLogsController::class)->name('system-logs.index')->can('viewAny', User::class);
    Route::get('/system-logs/{id}', SystemLogsShowController::class)->name('system-logs.show')->can('viewAny', User::class);
    // Settings
    Route::get('/settings', SettingsController::class)->name('settings.index')->can('viewAny', User::class);

    // Themes Management
    Route::get('/themes', ThemesController::class)->name('themes.index');

    Route::get('/test', LandingController::class)->name('land.index');
    Route::get('get', function () { return view('welcome'); });

    // Subscriptions
    Route::get('/subscriptions/subscribe/{planId?}', \App\Http\Controllers\WEB\Subscriptions\Subscribe::class)->name('subscriptions.subscribe');

    // Payment History
    Route::get('/payments/history', PaymentHistory::class)->name('payments.history');
    Route::get('/admin/payments', AdminPaymentHistory::class)->name('admin.payments.index');
});

// Webhook Routes
Route::post('/webhooks/paypal', [PayPalWebhook::class, 'handle'])->name('webhooks.paypal');
Route::post('/webhooks/stripe', [StripeWebhook::class, 'handle'])->name('webhooks.stripe');
Route::post('/webhooks/paymob', [PaymobWebhook::class, 'handle'])->name('webhooks.paymob');

// Route::get('/', function () {return view('w'); })->name('home');
Route::get('/', FrontController::class)->name('home');
Route::get('/register', RegisterForm::class)->name('register')->middleware(['registration.enabled', 'guest']);
Route::post('/register', function () {
    return redirect()->route('register');
})->middleware('registration.enabled');
Route::get('/profile/{username}', ProfileController::class)->name('publicProfile');
Route::get('/professional/{slug}', ProfessionalProfile::class)->name('professional.show');
Route::get('/language/{locale}', function ($locale) {
    $languages = Language::where('status', 1)->get();
    $availableLocales = $languages->pluck('short')->toArray();

    if (in_array($locale, $availableLocales)) {
        Session::put('lang', $locale);
        App::setLocale($locale);
    }

    return redirect()->back();
})->name('language.switch');
Route::get('/project/{slug}', ProjectsController::class)->name('project');
Route::get('/linktree/{slug}', FrontLinkTreeController::class)->name('linktree.show');
Route::get('/page/{slug}', FrontPagesController::class)->name('page.show');

Route::get('/sitemap.xml', function () {
    try {
        // جمع جميع البيانات المطلوبة لخريطة الموقع
        $projects = PortfolioModel::where('status', true)->get();
        $pages = Page::where('status', 1)->get();
        $languages = Language::where('status', 1)->get();
        $linkTrees = \App\Models\LinkTree::where('status', 1)->get();
        $publicProfiles = User::whereNotNull('username')
            ->where('profile_visibility', 'public')
            ->get();
        $professionalProfiles = \App\Models\Profile::where('visibility', 'public')
            ->where('is_active', true)
            ->get();
        $categories = Category::where('status', 1)->get();

        // إعدادات الموقع
        $registrationEnabled = \App\Helpers\SettingsHelper::isRegistrationEnabled();
        $front = \App\Helpers\SettingsHelper::getHomeData();

        $xml = view('sitemap', compact(
            'projects',
            'pages',
            'languages',
            'linkTrees',
            'publicProfiles',
            'professionalProfiles',
            'categories',
            'registrationEnabled',
            'front'
        ));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=86400', // Cache for 24 hours
            'ETag' => md5($xml),
            'Last-Modified' => gmdate('D, d M Y H:i:s').' GMT',
        ]);
    } catch (Exception $e) {
        // في حالة حدوث خطأ، إرجاع sitemap بسيط
        $projects = PortfolioModel::where('status', true)->get();
        $xml = view('sitemap-simple', compact('projects'));

        return Response::make($xml, 200, ['Content-Type' => 'application/xml']);
    }
});

// Dynamic robots.txt
Route::get('/robots.txt', function () {
    // Get robots.txt content from settings
    $customRobotsTxt = \App\Helpers\SettingsHelper::getRobotsTxt();

    if ($customRobotsTxt && !empty(trim($customRobotsTxt))) {
        $robotsContent = $customRobotsTxt;
    } else {
        // Default robots.txt content
        $robotsContent = '# Robots.txt for Digital Identity Platform
# Generated automatically - Last updated: '.date('Y-m-d H:i:s').'

# Allow all search engines
User-agent: *

# Allow access to main content
Allow: /
Allow: /page/
Allow: /project/
Allow: /profile/
Allow: /linktree/

# Disallow admin and private areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/
Disallow: /livewire/
Disallow: /storage/private/
Disallow: /vendor/
Disallow: /build/

# Disallow authentication pages
Disallow: /login
Disallow: /register
Disallow: /password/
Disallow: /email/

# Disallow temporary and cache files
Disallow: /tmp/
Disallow: /cache/
Disallow: /*.log$
Disallow: /*.tmp$

# Allow specific file types
Allow: /*.css$
Allow: /*.js$
Allow: /*.png$
Allow: /*.jpg$
Allow: /*.jpeg$
Allow: /*.gif$
Allow: /*.webp$
Allow: /*.svg$
Allow: /*.ico$
Allow: /*.pdf$

# Crawl delay for respectful crawling
Crawl-delay: 1

# Sitemap location
Sitemap: '.url('/sitemap.xml').'

# Special rules for different bots
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 2

User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

# Block bad bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /';
    }

    return Response::make($robotsContent, 200, [
        'Content-Type' => 'text/plain',
        'Cache-Control' => 'public, max-age=604800', // Cache for 1 week
        'ETag' => md5($robotsContent),
        'Last-Modified' => gmdate('D, d M Y H:i:s').' GMT',
    ]);
});

// Sitemap Index for large sites
Route::get('/sitemap-index.xml', function () {
    try {
        $projectsCount = PortfolioModel::where('status', true)->count();
        $profilesCount = User::whereNotNull('username')->where('profile_visibility', 'public')->count();
        $linkTreesCount = \App\Models\LinkTree::where('status', 1)->count();

        $projectsLastMod = PortfolioModel::where('status', true)->latest('updated_at')->first()?->updated_at?->toDateString();
        $profilesLastMod = User::whereNotNull('username')->where('profile_visibility', 'public')->latest('updated_at')->first()?->updated_at?->toDateString();
        $linkTreesLastMod = \App\Models\LinkTree::where('status', 1)->latest('updated_at')->first()?->updated_at?->toDateString();
        $pagesLastMod = Page::where('status', 1)->latest('updated_at')->first()?->updated_at?->toDateString();

        $xml = view('sitemap-index', compact(
            'projectsCount', 'profilesCount', 'linkTreesCount',
            'projectsLastMod', 'profilesLastMod', 'linkTreesLastMod', 'pagesLastMod'
        ));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// Main sitemap
Route::get('/sitemap-main.xml', function () {
    try {
        $languages = Language::where('status', 1)->get();
        $registrationEnabled = \App\Helpers\SettingsHelper::isRegistrationEnabled();
        $front = \App\Helpers\SettingsHelper::getHomeData();

        $xml = view('sitemap-main', compact('languages', 'registrationEnabled', 'front'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// Pages sitemap
Route::get('/sitemap-pages.xml', function () {
    try {
        $pages = Page::where('status', 1)->get();
        $languages = Language::where('status', 1)->get();
        $categories = Category::where('status', 1)->get();

        $xml = view('sitemap-pages', compact('pages', 'languages', 'categories'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// Projects sitemap
Route::get('/sitemap-projects.xml', function () {
    try {
        $projects = PortfolioModel::where('status', true)->get();

        $xml = view('sitemap-projects', compact('projects'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// Profiles sitemap
Route::get('/sitemap-profiles.xml', function () {
    try {
        $profiles = User::whereNotNull('username')
            ->where('profile_visibility', 'public')
            ->get();

        $xml = view('sitemap-profiles', compact('profiles'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// LinkTrees sitemap
Route::get('/sitemap-linktrees.xml', function () {
    try {
        $linkTrees = \App\Models\LinkTree::where('status', 1)->get();

        $xml = view('sitemap-linktrees', compact('linkTrees'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});

// Images sitemap
Route::get('/sitemap-images.xml', function () {
    try {
        $front = \App\Helpers\SettingsHelper::getHomeData();
        $projects = PortfolioModel::where('status', true)->get();
        $profiles = User::whereNotNull('username')
            ->where('profile_visibility', 'public')
            ->get();
        $linkTrees = \App\Models\LinkTree::where('status', 1)->get();

        $xml = view('sitemap-images', compact('front', 'projects', 'profiles', 'linkTrees'));

        return Response::make($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    } catch (Exception $e) {
        return redirect('/sitemap.xml');
    }
});
