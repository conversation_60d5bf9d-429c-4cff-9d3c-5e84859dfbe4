<?php

use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CollectionController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Api\PagesController;
use App\Http\Controllers\Api\SocialsController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
Route::get('test', function () {
    return response([1, 2, 3, 4], 200);
});
Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);

Route::post('forget-password', [AuthController::class, 'forgetPassword']);
Route::post('resend-code', [AuthController::class, 'resendCode']);
Route::post('verify-code', [AuthController::class, 'verifyCode']);

Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
    // Route::post('/update-profile', [UserController::class, 'updateProfile']);

    Route::post('logout', function (Request $request) {
        $request->user()->token()->revoke();

        return response()->json([
            'message' => 'User has been logged out',
        ]);
    });
});

// Unauth

Route::get('collection/{uuser}/{cname}', [CollectionController::class, 'getApiRoute'])->name('collection.show');
// Route::get('home', [HomeController::class, 'home']);
// Route::get('calendar', [HomeController::class, 'calendarWithPrayerTime']);
// Route::get('page/{slug}', [PagesController::class, 'page']);
// Route::get('socials', [SocialsController::class, 'socials']);

Route::group(['prefix' => 'guest'], function (): void {
});

// Analytics routes
Route::post('analytics/track-link-click', [AnalyticsController::class, 'trackLinkClick']);
Route::post('analytics/track-social-click', [AnalyticsController::class, 'trackSocialClick']);
Route::post('analytics/track-page-visit', [AnalyticsController::class, 'trackPageVisit']);
