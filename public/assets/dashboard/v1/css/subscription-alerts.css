/* Subscription Limit Alert Styles */
.subscription-limit-alert {
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.subscription-limit-alert .swal2-title {
    color: #dc3545 !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

.subscription-limit-alert .swal2-html-container {
    color: #6c757d !important;
    line-height: 1.6 !important;
}

.subscription-limit-alert .swal2-html-container p {
    margin-bottom: 0.75rem !important;
}

.subscription-limit-alert .swal2-html-container small {
    display: block !important;
    padding: 0.5rem 1rem !important;
    background-color: #f8f9fa !important;
    border-radius: 6px !important;
    border-left: 3px solid #ffc107 !important;
    font-weight: 500 !important;
    color: #495057 !important;
}

.subscription-limit-alert .swal2-actions {
    margin-top: 1.5rem !important;
    gap: 0.75rem !important;
}

.subscription-limit-alert .swal2-confirm {
    background-color: #3085d6 !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.subscription-limit-alert .swal2-confirm:hover {
    background-color: #2574c7 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(48, 133, 214, 0.3) !important;
}

.subscription-limit-alert .swal2-cancel {
    background-color: #6c757d !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.subscription-limit-alert .swal2-cancel:hover {
    background-color: #5a6268 !important;
    transform: translateY(-1px) !important;
}

/* Icon styling */
.subscription-limit-alert .swal2-icon.swal2-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

/* Animation for better UX */
.subscription-limit-alert.swal2-show {
    animation: swal2-show 0.3s ease-out !important;
}

@keyframes swal2-show {
    0% {
        transform: scale(0.7) translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* RTL Support */
html[dir="rtl"] .subscription-limit-alert .swal2-html-container small {
    border-left: none !important;
    border-right: 3px solid #ffc107 !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .subscription-limit-alert {
        width: 95% !important;
        margin: 0 auto !important;
    }
    
    .subscription-limit-alert .swal2-actions {
        flex-direction: column !important;
        width: 100% !important;
    }
    
    .subscription-limit-alert .swal2-confirm,
    .subscription-limit-alert .swal2-cancel {
        width: 100% !important;
        margin: 0.25rem 0 !important;
    }
}