/**
 * Subscription Alerts Handler
 * Handles subscription limit alerts and redirects to upgrade page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Listen for Livewire alerts
    document.addEventListener('livewire:init', () => {
        Livewire.on('alert', (data) => {
            handleSubscriptionAlert(data);
        });
    });

    // Handle SweetAlert2 confirm button clicks for upgrade
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('swal2-confirm')) {
            const buttonText = e.target.textContent || e.target.innerText;
            if (buttonText.includes('Upgrade') || buttonText.includes('ترقية')) {
                e.preventDefault();
                redirectToUpgrade();
            }
        }
    });
});

/**
 * Handle subscription alert and add upgrade functionality
 * @param {Object} data - Alert data from Livewire
 */
function handleSubscriptionAlert(data) {
    if (data && data.type === 'error' && data.confirmButtonText) {
        const confirmText = data.confirmButtonText;
        if (confirmText.includes('Upgrade') || confirmText.includes('ترقية')) {
            // Override SweetAlert2 behavior for upgrade button
            setTimeout(() => {
                const confirmButton = document.querySelector('.swal2-confirm');
                if (confirmButton) {
                    confirmButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        redirectToUpgrade();
                    });
                }
            }, 100);
        }
    }
}

/**
 * Redirect to subscription upgrade page
 */
function redirectToUpgrade() {
    // Close any open SweetAlert2 modals
    if (window.Swal) {
        Swal.close();
    }
    
    // Redirect to subscription page
    window.location.href = '/dashboard/subscriptions/subscribe';
}

/**
 * Show custom subscription limit alert
 * @param {string} title - Alert title
 * @param {string} message - Alert message
 * @param {string} details - Additional details
 * @param {string} upgradeText - Upgrade button text
 * @param {string} closeText - Close button text
 */
function showSubscriptionLimitAlert(title, message, details, upgradeText, closeText) {
    if (window.Swal) {
        Swal.fire({
            title: title,
            html: `<div class="text-center">
                     <p class="mb-3">${message}</p>
                     <small class="text-muted">${details}</small>
                   </div>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#6c757d',
            confirmButtonText: upgradeText,
            cancelButtonText: closeText,
            reverseButtons: true,
            customClass: {
                popup: 'subscription-limit-alert',
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                redirectToUpgrade();
            }
        });
    }
}

// Export functions for global use
window.showSubscriptionLimitAlert = showSubscriptionLimitAlert;
window.redirectToUpgrade = redirectToUpgrade;