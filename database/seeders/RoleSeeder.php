<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Role::firstOrCreate([
            'name' => 'Administrator',
            'key' => 'administrator',
            'color' => 'bg-red-700 text-white',
        ]);

        Role::firstOrCreate([
            'name' => 'Admin',
            'key' => 'admin',
            'color' => 'bg-orange-600 text-white',
        ]);

        Role::firstOrCreate([
            'name' => 'Moderator',
            'key' => 'moderator',
            'color' => 'bg-green-600 text-white',
        ]);

        Role::firstOrCreate([
            'name' => 'User',
            'key' => 'user',
            'color' => 'bg-gray-100 text-black',
        ]);

        Role::firstOrCreate([
            'name' => 'Banned',
            'key' => 'banned',
            'color' => 'bg-gray-600 text-white',
        ]);

        $permission_administrator = Permission::where('key', '!=', 'banned')->pluck('id')->toArray();
        $linktree_permissions = Permission::where('tableName', 'linktrees')->pluck('id')->toArray();
        $profile_permissions = Permission::where('tableName', 'profiles')->pluck('id')->toArray();
        $user_permissions = array_merge($linktree_permissions, $profile_permissions);

        $admin_role = Role::where('key', 'administrator')->first();
        $admin_role->permission()->sync($permission_administrator);

        $user_role = Role::where('key', 'user')->first();
        $user_role->permission()->sync($user_permissions);
    }
}
