<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call(LanguageSeeder::class);
        $this->call(SettingsSeeder::class);
        $this->call(PermissionSeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(PageSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(AboutSeeder::class);
        $this->call(DesignSeeder::class);
        $this->call(SocialSeeder::class);

        // Subscription System Seeders
        $this->call(LimitTypeSeeder::class);
        $this->call(FeatureSeeder::class);
        $this->call(PlanSeeder::class);

        // Homepage Seeder
        $this->call(HomeSeeder::class);

        // Services Pages Seeders
        $this->call(ServicesPageSeeder::class);
        $this->call(AdditionalServicesSeeder::class);
        $this->call(UpdatedPagesSeeder::class);
    }
}
