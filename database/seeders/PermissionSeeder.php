<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::firstOrCreate([
            'name' => 'Browse admin',
            'key' => 'browse_admin',
            'tableName' => null,
        ]);

        Permission::firstOrCreate([
            'name' => 'Administrator',
            'key' => 'administrator',
            'tableName' => null,
        ]);

        Permission::firstOrCreate([
            'name' => 'Banned',
            'key' => 'banned',
            'tableName' => null,
        ]);

        Permission::generateFor('permissions');
        Permission::generateFor('languages');
        Permission::generateFor('settings');
        Permission::generateFor('homes');
        Permission::generateFor('roles');
        Permission::generateFor('plans');
        Permission::generateFor('pages');
        Permission::generateFor('features');
        Permission::generateFor('limit_types');
        Permission::generateFor('subscriptions');
        Permission::generateFor('users');
        Permission::generateFor('about');
        Permission::generateFor('skills');
        Permission::generateFor('experience');
        Permission::generateFor('education');
        Permission::generateFor('categories');
        Permission::generateFor('portfolio');
        Permission::generateFor('designs');
        Permission::generateFor('socials');
        Permission::generateFor('messages');
        Permission::generateFor('collections');
        Permission::generateFor('linktrees');
        Permission::generateFor('companies');
        Permission::generateFor('products');
        Permission::generateFor('stores');
        Permission::generateFor('projects');
        Permission::generateFor('payments');
        Permission::generateFor('profiles');
    }
}
