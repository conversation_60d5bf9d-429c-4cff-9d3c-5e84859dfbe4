<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsSeeder extends Seeder
{
    public function run()
    {
        // Check if settings already exist
        if (DB::table('settings')->exists()) {
            // Update existing settings with payment gateway defaults
            DB::table('settings')->update([
                'payment_stripe_enabled' => true,
                'payment_paypal_enabled' => true,
                'payment_paymob_enabled' => false,
                'payment_credit_card_enabled' => true,
                'paymob_sandbox' => true,
                'payment_methods_description' => 'We accept all major credit cards, PayPal, and bank transfers.',
                'payment_method_credit_card' => true,
                'payment_method_paypal' => true,
                'payment_method_bank_transfer' => false,
                'payment_method_cash' => false,
                'gateway_stripe_enabled' => true,
                'gateway_paypal_enabled' => true,
                'gateway_paymob_enabled' => false,
                'updated_at' => now(),
            ]);
            return;
        }
        
        DB::table('settings')->insert([
            // Basic Site Information
            'site_name' => json_encode([
                'en' => 'Fanzly',
                'ar' => 'فانزلي',
            ]),
            'site_description' => json_encode([
                'en' => 'All-in-One Platform for Your Digital Identity',
                'ar' => 'منصة متكاملة لإنشاء هويتك الرقمية',
            ]),
            'site_keywords' => json_encode([
                'en' => 'fanzly, linktree, digital identity, profile, portfolio, business profile',
                'ar' => 'فانزلي, شجرة روابط, هوية رقمية, ملف شخصي, معرض أعمال, ملف أعمال',
            ]),
            'site_tagline' => json_encode([
                'en' => 'Professional Tools to Build Your Online Presence',
                'ar' => 'أدوات احترافية لبناء تواجدك على الإنترنت',
            ]),
            'site_url' => 'https://fanzly.net',
            'admin_email' => '<EMAIL>',
            'support_email' => '<EMAIL>',
            'noreply_email' => '<EMAIL>',

            // Language & Localization
            'default_language' => 1, // Assuming Arabic is ID 1
            'timezone' => 'Asia/Dubai',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'currency_position' => 'before',

            // Contact Information
            'contact_address' => json_encode([
                'en' => 'Cairo, Egypt',
                'ar' => 'القاهرة، مصر',
            ]),
            'contact_phone' => '+201020904386',
            'contact_whatsapp' => '+201020904386',

            // SEO Settings
            'meta_title' => json_encode([
                'en' => 'Fanzly - All-in-One Platform for Your Digital Identity',
                'ar' => 'فانزلي - منصة متكاملة لإنشاء هويتك الرقمية',
            ]),
            'meta_description' => json_encode([
                'en' => 'Professional tools to build your online presence. Create Link Tree pages, premium profiles, business profiles, and e-commerce stores.',
                'ar' => 'أدوات احترافية لبناء تواجدك على الإنترنت. أنشئ صفحات لينك تري وملفات مميزة وملفات أعمال ومتاجر إلكترونية.',
            ]),
            'robots_txt' => "User-agent: *\nAllow: /\nSitemap: https://fanzly.net/sitemap.xml",

            // Security Settings
            'maintenance_mode' => false,
            'maintenance_message' => json_encode([
                'en' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'ar' => 'نقوم حالياً بصيانة مجدولة. يرجى المراجعة قريباً.',
            ]),
            'user_registration' => true,
            'email_verification' => false,
            'two_factor_auth' => false,
            'session_lifetime' => 120,
            'allowed_file_types' => json_encode(['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'doc', 'docx']),
            'max_file_size' => 2048,

            // Notification Settings
            'email_notifications' => true,
            'sms_notifications' => false,
            'push_notifications' => false,

            // Performance Settings
            'cache_enabled' => true,
            'cache_duration' => 3600,
            'image_optimization' => true,
            'lazy_loading' => true,
            'minify_css' => false,
            'minify_js' => false,

            // Business Settings
            'business_name' => json_encode([
                'en' => 'Fanzly',
                'ar' => 'فانزلي',
            ]),
            'terms_of_service' => json_encode([
                'en' => 'Terms of Service content here...',
                'ar' => 'محتوى شروط الخدمة هنا...',
            ]),
            'privacy_policy' => json_encode([
                'en' => 'Privacy Policy content here...',
                'ar' => 'محتوى سياسة الخصوصية هنا...',
            ]),

            // Theme Settings
            'default_theme' => 'light',
            'theme_colors' => json_encode([
                'primary' => '#667eea',
                'secondary' => '#764ba2',
                'success' => '#22C55E',
                'warning' => '#ffd700',
                'danger' => '#ff6b6b',
                'info' => '#06B6D4',
                'dark' => '#2d3748',
                'light' => '#f8f9ff',
            ]),
            'dark_mode_enabled' => true,
            'font_family' => 'Inter',

            // Payment Gateway Settings
            'payment_stripe_enabled' => true,
            'stripe_public_key' => null,
            'stripe_secret_key' => null,
            'stripe_webhook_secret' => null,
            
            'payment_paypal_enabled' => true,
            'paypal_client_id' => null,
            'paypal_client_secret' => null,
            'paypal_sandbox' => true,
            'paypal_webhook_id' => null,
            'paypal_webhook_secret' => null,
            
            'payment_paymob_enabled' => false,
            'paymob_api_key' => null,
            'paymob_integration_id' => null,
            'paymob_iframe_id' => null,
            'paymob_hmac_secret' => null,
            'paymob_sandbox' => true,
            'paymob_base_url' => null,
            
            'payment_credit_card_enabled' => true,
            'payment_methods_description' => 'We accept all major credit cards, PayPal, and bank transfers.',
            'payment_method_credit_card' => true,
            'payment_method_paypal' => true,
            'payment_method_bank_transfer' => false,
            'payment_method_cash' => false,
            'gateway_stripe_enabled' => true,
            'gateway_paypal_enabled' => true,
            'gateway_paymob_enabled' => false,

            // Backup & Logs
            'auto_backup' => false,
            'backup_frequency' => 'weekly',
            'error_logging' => true,
            'activity_logging' => true,
            'log_retention_days' => 30,

            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
