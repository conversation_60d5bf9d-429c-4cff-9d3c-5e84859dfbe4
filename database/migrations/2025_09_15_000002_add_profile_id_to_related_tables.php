<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add profile_id to skills table
        Schema::table('skills', function (Blueprint $table) {
            $table->foreignId('profile_id')->nullable()->constrained('profiles')->nullOnDelete()->after('user_id');
        });

        // Add profile_id to education table
        Schema::table('education', function (Blueprint $table) {
            $table->foreignId('profile_id')->nullable()->constrained('profiles')->nullOnDelete()->after('user_id');
        });

        // Add profile_id to experiences table
        Schema::table('experiences', function (Blueprint $table) {
            $table->foreignId('profile_id')->nullable()->constrained('profiles')->nullOnDelete()->after('user_id');
        });

        // Add profile_id to categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->foreignId('profile_id')->nullable()->constrained('profiles')->nullOnDelete()->after('user_id');
        });

        // Add profile_id to portfolios table
        Schema::table('portfolios', function (Blueprint $table) {
            $table->foreignId('profile_id')->nullable()->constrained('profiles')->nullOnDelete()->after('user_id');
        });
    }

    public function down(): void
    {
        Schema::table('skills', function (Blueprint $table) {
            $table->dropForeign(['profile_id']);
            $table->dropColumn('profile_id');
        });

        Schema::table('education', function (Blueprint $table) {
            $table->dropForeign(['profile_id']);
            $table->dropColumn('profile_id');
        });

        Schema::table('experiences', function (Blueprint $table) {
            $table->dropForeign(['profile_id']);
            $table->dropColumn('profile_id');
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropForeign(['profile_id']);
            $table->dropColumn('profile_id');
        });

        Schema::table('portfolios', function (Blueprint $table) {
            $table->dropForeign(['profile_id']);
            $table->dropColumn('profile_id');
        });
    }
};