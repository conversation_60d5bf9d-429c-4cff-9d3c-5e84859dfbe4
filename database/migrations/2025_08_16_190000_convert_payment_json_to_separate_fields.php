<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::table('settings', function (Blueprint $table) {
            // Remove JSON fields and add separate boolean fields
            $table->dropColumn(['payment_methods', 'enabled_payment_gateways']);
            
            // Add individual payment method fields
            $table->boolean('payment_method_credit_card')->default(true)->after('payment_credit_card_enabled');
            $table->boolean('payment_method_paypal')->default(true)->after('payment_method_credit_card');
            $table->boolean('payment_method_bank_transfer')->default(false)->after('payment_method_paypal');
            $table->boolean('payment_method_cash')->default(false)->after('payment_method_bank_transfer');
            
            // Add gateway enable fields
            $table->boolean('gateway_stripe_enabled')->default(true)->after('payment_method_cash');
            $table->boolean('gateway_paypal_enabled')->default(true)->after('gateway_stripe_enabled');
            $table->boolean('gateway_paymob_enabled')->default(false)->after('gateway_paypal_enabled');
        });
    }

    public function down()
    {
        Schema::table('settings', function (Blueprint $table) {
            // Remove separate fields
            $table->dropColumn([
                'payment_method_credit_card',
                'payment_method_paypal', 
                'payment_method_bank_transfer',
                'payment_method_cash',
                'gateway_stripe_enabled',
                'gateway_paypal_enabled',
                'gateway_paymob_enabled'
            ]);
            
            // Add back JSON fields
            $table->json('payment_methods')->nullable();
            $table->json('enabled_payment_gateways')->nullable();
        });
    }
};