<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('themes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->cascadeOnUpdate()->nullOnDelete();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('version')->default('1.0.0');
            $table->string('author')->nullable();
            $table->text('description')->nullable();
            $table->string('directory_path')->comment('مسار مجلد القالب');
            $table->string('view_file')->default('profile.blade.php')->comment('ملف العرض الرئيسي');
            $table->enum('type', ['link_tree', 'profile', 'company', 'product', 'store', 'project'])->default('profile');
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->string('primary_color', 20)->nullable();
            $table->string('second_color', 20)->nullable();
            $table->string('font')->nullable();
            $table->string('background_color', 20)->nullable();
            $table->string('background_image')->nullable();
            $table->json('config_data')->nullable()->comment('إعدادات الثيم من config.json');
            $table->json('assets_manifest')->nullable()->comment('قائمة ملفات الثيم');
            $table->json('customizable_options')->nullable()->comment('خيارات التخصيص المتاحة');
            $table->timestamp('installation_date')->nullable();
            $table->timestamp('last_updated')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('themes');
    }
};
