<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->string('slug')->unique()->after('user_id');
            $table->json('name')->after('slug');
            $table->json('bio')->nullable()->after('name');
            $table->json('job_title')->nullable()->after('bio');
            $table->string('email')->nullable()->after('job_title');
            $table->string('phone')->nullable()->after('email');
            $table->string('website')->nullable()->after('phone');
            $table->string('domain')->nullable()->after('website');
            $table->json('address')->nullable()->after('domain');
            $table->foreignId('theme_id')->nullable()->constrained('themes')->nullOnDelete()->after('address');
            $table->json('settings')->nullable()->after('theme_id');
            $table->json('seo_data')->nullable()->after('settings');
            $table->enum('visibility', ['public', 'private', 'unlisted'])->default('public')->after('seo_data');
            $table->boolean('is_active')->default(true)->after('visibility');
            $table->softDeletes()->after('is_active');
        });
    }

    public function down(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropColumn([
                'slug', 'name', 'bio', 'job_title', 'email', 'phone',
                'website', 'domain', 'address', 'theme_id', 'settings', 'seo_data',
                'visibility', 'is_active',
            ]);
        });
    }
};
