<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('plan_id')->nullable()->constrained()->onDelete('set null');
            $table->string('payment_id')->unique(); // معرف الدفع من البوابة
            $table->string('gateway'); // paypal, stripe, paymob
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('type', ['subscription', 'upgrade', 'renewal', 'refund'])->default('subscription');
            $table->json('gateway_data')->nullable(); // بيانات البوابة الكاملة
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['gateway', 'status']);
            $table->index('paid_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('payments');
    }
};