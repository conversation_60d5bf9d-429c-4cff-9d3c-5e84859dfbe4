<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('settings', function (Blueprint $table) {
            // Payment gateway enable/disable flags
            $table->boolean('payment_stripe_enabled')->default(true)->after('payment_methods');
            $table->boolean('payment_paypal_enabled')->default(true)->after('payment_stripe_enabled');
            $table->boolean('payment_paymob_enabled')->default(false)->after('payment_paypal_enabled');
            $table->boolean('payment_credit_card_enabled')->default(true)->after('payment_paymob_enabled');
            
            // Additional payment fields
            $table->string('stripe_webhook_secret')->nullable()->after('stripe_secret_key');
            $table->string('paypal_webhook_id')->nullable()->after('paypal_sandbox');
            $table->string('paypal_webhook_secret')->nullable()->after('paypal_webhook_id');
            
            // Paymob fields
            $table->text('paymob_api_key')->nullable()->after('paypal_webhook_secret');
            $table->string('paymob_integration_id')->nullable()->after('paymob_api_key');
            $table->string('paymob_iframe_id')->nullable()->after('paymob_integration_id');
            $table->string('paymob_hmac_secret')->nullable()->after('paymob_iframe_id');
            $table->boolean('paymob_sandbox')->default(true)->after('paymob_hmac_secret');
            $table->string('paymob_base_url')->nullable()->after('paymob_sandbox');
            
            // Additional payment settings
            $table->text('payment_methods_description')->nullable()->after('payment_credit_card_enabled');
            $table->json('enabled_payment_gateways')->nullable()->after('payment_methods_description');
        });
    }

    public function down()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn([
                'payment_stripe_enabled',
                'payment_paypal_enabled', 
                'payment_paymob_enabled',
                'payment_credit_card_enabled',
                'stripe_webhook_secret',
                'paypal_webhook_id',
                'paypal_webhook_secret',
                'paymob_api_key',
                'paymob_integration_id',
                'paymob_iframe_id',
                'paymob_hmac_secret',
                'paymob_sandbox',
                'paymob_base_url',
                'payment_methods_description',
                'enabled_payment_gateways'
            ]);
        });
    }
};