<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('link_trees', function (Blueprint $table) {
            // Personal Information
            $table->json('alternate_names')->nullable()->after('keywords'); // Alternative names in different languages
            $table->string('email')->nullable()->after('alternate_names');
            $table->string('phone')->nullable()->after('email');
            $table->date('birth_date')->nullable()->after('phone');

            // Address Information
            $table->json('address')->nullable()->after('birth_date'); // Street, city, country, postal code
            $table->json('birth_place')->nullable()->after('address'); // Birth place information

            // Professional Information
            $table->json('works_for')->nullable()->after('birth_place'); // Organization information
            $table->json('education')->nullable()->after('works_for'); // Educational background
            $table->json('skills')->nullable()->after('education'); // Skills array
            $table->json('languages')->nullable()->after('skills'); // Known languages
            $table->json('knows_about')->nullable()->after('languages'); // Areas of expertise

            // Business Information (if applicable)
            $table->json('business_info')->nullable()->after('knows_about'); // Business details
            $table->string('price_range')->nullable()->after('business_info'); // Price range for services

            // SEO Settings
            $table->boolean('enable_rich_snippets')->default(false)->after('price_range');
            $table->json('custom_schema')->nullable()->after('enable_rich_snippets'); // Custom schema.org data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('link_trees', function (Blueprint $table) {
            $table->dropColumn([
                'alternate_names',
                'email',
                'phone',
                'birth_date',
                'address',
                'birth_place',
                'works_for',
                'education',
                'skills',
                'languages',
                'knows_about',
                'business_info',
                'price_range',
                'enable_rich_snippets',
                'custom_schema',
            ]);
        });
    }
};
