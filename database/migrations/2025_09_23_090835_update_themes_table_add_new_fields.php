<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('themes', function (Blueprint $table) {
            // Add new fields
            $table->foreignId('user_id')->nullable()->after('id')->constrained('users')->cascadeOnUpdate()->nullOnDelete();
            $table->string('view_file')->default('profile.blade.php')->after('directory_path')->comment('ملف العرض الرئيسي');
            $table->json('config_data')->nullable()->after('background_image')->comment('إعدادات الثيم من config.json');
            $table->json('assets_manifest')->nullable()->after('config_data')->comment('قائمة ملفات الثيم');
            $table->json('customizable_options')->nullable()->after('assets_manifest')->comment('خيارات التخصيص المتاحة');
            $table->timestamp('installation_date')->nullable()->after('customizable_options');
            $table->timestamp('last_updated')->nullable()->after('installation_date');
            $table->softDeletes()->after('last_updated');

            // Modify existing fields
            $table->string('name')->nullable(false)->change();
            $table->string('slug')->unique()->nullable(false)->change();
            $table->text('description')->nullable()->change();
            $table->string('primary_color', 20)->nullable()->change();
            $table->string('second_color', 20)->nullable()->change();
            $table->string('background_color', 20)->nullable()->change();
            $table->boolean('is_default')->default(false)->change();

            // Fix enum typo
            $table->enum('type', ['link_tree', 'profile', 'company', 'product', 'store', 'project'])->default('profile')->change();

            // Add indexes
            $table->index(['type', 'is_active']);
            $table->index('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('themes', function (Blueprint $table) {
            // Remove new fields
            $table->dropForeign(['user_id']);
            $table->dropColumn([
                'user_id', 'view_file', 'config_data', 'assets_manifest',
                'customizable_options', 'installation_date', 'last_updated'
            ]);
            $table->dropSoftDeletes();

            // Remove indexes
            $table->dropIndex(['themes_type_is_active_index']);
            $table->dropIndex(['themes_is_default_index']);

            // Revert enum
            $table->enum('type', ['link_tree', 'profile', 'company', 'produc', 'store', 'project'])->default('link_tree')->change();
        });
    }
};
