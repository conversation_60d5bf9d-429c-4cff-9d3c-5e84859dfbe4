<div class="page-header">
    <nav class="navbar navbar-expand-lg d-flex justify-content-between">
        <div class="" id="navbarNav">
            <ul class="navbar-nav" id="leftNav">
                <li class="nav-item">
                    <a class="nav-link" id="sidebar-toggle" href="#"><i data-feather="arrow-left"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" target="__blank" href="{{ url('/') }}">{{ __('Home Page') }}</a>
                </li>
            </ul>
        </div>
        <div class="d-flex justify-content-center" id="navbarNav">
            <ul class="navbar-nav d-flex justify-content-center" id="centerNav">
                @if (auth()->user()->role_id == 4 && auth()->user()->activeSubscription)
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('linktree.index') }}">{{ __('linktree') }}</a>
                    </li>
                    @if(in_array('profiles', session('available_features', [])) || in_array('personal-profile', session('available_features', [])))
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('profiles.index') }}">{{ __('Professional Profiles') }}</a>
                    </li>
                    @endif
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdownMenuLink" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            {{ __('Tools') }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <li><a class="dropdown-item"
                                    href="{{ route('collections.index') }}">{{ __('Collection Builder') }}</a></li>
                        </ul>
                    </li>
                @endif
            </ul>
        </div>
        <div class="" id="headerNav">
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link search-dropdown" href="#" id="searchDropDown" role="button"
                        data-bs-toggle="dropdown" aria-expanded="false">{{ $languageName ?? '' }}</a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-lg search-drop-menu"
                        aria-labelledby="searchDropDown">
                        @forelse ($availablelanguages as $lang)
                            @if ($lang->name != $languageName)
                                <a href="{{ route('language.switch', $lang->short) }}" type="button"
                                    class="dropdown-item">
                                    {{ $lang->name }}
                                </a>
                            @endif
                        @empty
                        @endforelse
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link notifications-dropdown" href="#" id="notificationsDropDown" role="button"
                        data-bs-toggle="dropdown" aria-expanded="false">{{ $myMessages->count() }}</a>
                    <div class="dropdown-menu dropdown-menu-end notif-drop-menu"
                        aria-labelledby="notificationsDropDown">
                        <h6 class="dropdown-header">Notifications</h6>
                        @forelse ($myMessages as $message)
                            {{-- <a href="{{ route('messages.show', $message->id) }}" class="dropdown-item"> --}}
                            <a href="#">
                                <div class="header-notif">
                                    <div class="notif-text">
                                        <p class="bold-notif-text">{{ $message->name }}</p>
                                        <small>{{ $message->created_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                            </a>
                        @empty
                            <a href="#">
                                <div class="header-notif">
                                    <div class="notif-text">
                                        <p class="bold-notif-text">{{ __('No Data') }}</p>
                                    </div>
                                </div>
                            </a>
                        @endforelse
                    </div>
                </li>
                <li class="nav-item dropdown">

                    <a class="nav-link profile-dropdown" href="#" id="profileDropDown" role="button"
                        data-bs-toggle="dropdown" aria-expanded="false"><img
                            src="{{ Auth::user()->profile_photo_url }}" alt="{{ Auth::user()->name }}"></a>
                    <div class="dropdown-menu dropdown-menu-end profile-drop-menu" aria-labelledby="profileDropDown">
                        <a class="dropdown-item" href="{{ route('profile') }}"><i data-feather="user"></i>Profile</a>
                        <div class="dropdown-divider"></div>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button class="dropdown-item" href="#"><i data-feather="log-out"
                                    onclick="event.preventDefault(); this.closest('form').submit();"></i>Logout</button>
                        </form>
                    </div>
                </li>
            </ul>
        </div>
    </nav>
</div>
