<div class="page-sidebar">
    <ul class="list-unstyled accordion-menu">
        @can('viewAny', App\Models\Dashboard::class)
        <li class="{{ request()->routeIs('dashboard') ? 'active-page' : '' }}">
            <a href="{{ route('dashboard') }}" class="{{ request()->routeIs('dashboard') ? 'active' : '' }}">
                <i data-feather="home" class="nav-icon"></i>
                <span data-key="t-home">{{ __('Statistics') }}</span>
            </a>
        </li>
        @endcan

        @if(auth()->user()->role_id != 4 || in_array('home', session('available_features', [])))
            @can('viewAny', App\Models\Home::class)
            <li class="{{ request()->routeIs('home.index') ? 'active-page' : '' }}">
                <a href="{{ route('home.index') }}" class="{{ request()->routeIs('home.index') ? 'active' : '' }}">
                    <i data-feather="layout" class="nav-icon"></i>
                    <span>{{ __('Home Page') }}</span>
                </a>
            </li>
            @endcan
        @endif

        @if(auth()->user()->role_id != 4 || in_array('linktree', session('available_features', [])))
            @can('viewAny', App\Models\LinkTree::class)
            <li class="{{ request()->routeIs('linktree.index') ? 'active-page' : '' }}">
                <a href="{{ route('linktree.index') }}" class="{{ request()->routeIs('linktree.index') ? 'active' : '' }}">
                    <i data-feather="link-2" class="nav-icon"></i>
                    <span>{{ __('linktree') }}</span>
                </a>
            </li>
            @endcan
        @endif

        @if(auth()->user()->role_id != 4 || in_array('profiles', session('available_features', [])) || in_array('personal-profile', session('available_features', [])))
            @can('viewAny', App\Models\Profile::class)
            <li class="{{ request()->routeIs(['profiles.index', 'profiles.create', 'profiles.update']) ? 'active-page' : '' }}">
                <a href="{{ route('profiles.index') }}" class="{{ request()->routeIs(['profiles.index', 'profiles.create', 'profiles.update']) ? 'active' : '' }}">
                    <i data-feather="user" class="nav-icon"></i>
                    <span>{{ __('Professional Profiles') }}</span>
                </a>
            </li>
            @endcan
        @endif
        @if(auth()->user()->can('viewAny', App\Models\Role::class) ||
            auth()->user()->can('viewAny', App\Models\User::class) ||
            auth()->user()->can('viewAny', App\Models\Language::class) ||
            auth()->user()->can('viewAny', App\Models\Feature::class) ||
            auth()->user()->can('viewAny', App\Models\Plan::class) ||
            auth()->user()->can('viewAny', App\Models\Page::class))
        <li class="{{ request()->routeIs(['roles.index', 'users.index', 'languages.index', 'features.index', 'plans.index', 'pages.index', 'themes.index']) ? 'active-page' : '' }}">
            <a href="#"><i data-feather="settings" class="nav-icon"></i>
                <span>{{ __('Settings') }}</span>
                <i class="fas fa-chevron-right dropdown-icon"></i>
            </a>
            <ul>
                @can('viewAny', App\Models\Role::class)
                <li class="{{ request()->routeIs('roles.index') ? 'active-page' : '' }}">
                    <a href="{{ route('roles.index') }}" class="{{ request()->routeIs('roles.index') ? 'active' : '' }}">
                        <i data-feather="sliders" class="nav-icon"></i>
                        <span>{{ __('roles') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\User::class)
                <li class="{{ request()->routeIs('users.index') ? 'active-page' : '' }}">
                    <a href="{{ route('users.index') }}" class="{{ request()->routeIs('users.index') ? 'active' : '' }}">
                        <i data-feather="users" class="nav-icon"></i>
                        <span>{{ __('Users') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\Language::class)
                <li class="{{ request()->routeIs('languages.index') ? 'active-page' : '' }}">
                    <a href="{{ route('languages.index') }}" class="{{ request()->routeIs('languages.index') ? 'active' : '' }}">
                        <i data-feather="globe" class="nav-icon"></i>
                        <span>{{ __('languages') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\Page::class)
                <li class="{{ request()->routeIs('pages.index') ? 'active-page' : '' }}">
                    <a href="{{ route('pages.index') }}" class="{{ request()->routeIs('pages.index') ? 'active' : '' }}">
                        <i data-feather="file-text" class="nav-icon"></i>
                        <span>{{ __('Pages') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\Feature::class)
                <li class="{{ request()->routeIs('features.index') ? 'active-page' : '' }}">
                    <a href="{{ route('features.index') }}" class="{{ request()->routeIs('features.index') ? 'active' : '' }}">
                        <i data-feather="pie-chart" class="nav-icon"></i>
                        <span>{{ __('Features') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\Plan::class)
                <li class="{{ request()->routeIs('plans.index') ? 'active-page' : '' }}">
                    <a href="{{ route('plans.index') }}" class="{{ request()->routeIs('plans.index') ? 'active' : '' }}">
                        <i data-feather="package" class="nav-icon"></i>
                        <span>{{ __('Plans') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\User::class)
                <li class="{{ request()->routeIs(['system-logs.index', 'system-logs.show']) ? 'active-page' : '' }}">
                    <a href="{{ route('system-logs.index') }}" class="{{ request()->routeIs(['system-logs.index', 'system-logs.show']) ? 'active' : '' }}">
                        <i data-feather="activity" class="nav-icon"></i>
                        <span>{{ __('System Logs') }}</span>
                    </a>
                </li>
                @endcan

                @can('viewAny', App\Models\Setting::class)
                <li class="{{ request()->routeIs('settings.index') ? 'active-page' : '' }}">
                    <a href="{{ route('settings.index') }}" class="{{ request()->routeIs('settings.index') ? 'active' : '' }}">
                        <i data-feather="tool" class="nav-icon"></i>
                        <span>{{ __('General Settings') }}</span>
                    </a>
                </li>
                @endcan

                <li class="{{ request()->routeIs('themes.index') ? 'active-page' : '' }}">
                    <a href="{{ route('themes.index') }}" class="{{ request()->routeIs('themes.index') ? 'active' : '' }}">
                        <i data-feather="palette" class="nav-icon"></i>
                        <span>{{ __('إدارة الثيمات') }}</span>
                    </a>
                </li>

                @if(auth()->user()->hasPermission('payments.view'))
                <li class="{{ request()->routeIs('admin.payments.index') ? 'active-page' : '' }}">
                    <a href="{{ route('admin.payments.index') }}" class="{{ request()->routeIs('admin.payments.index') ? 'active' : '' }}">
                        <i data-feather="credit-card" class="nav-icon"></i>
                        <span>{{ __('Payment Management') }}</span>
                    </a>
                </li>
                @endif
            </ul>
        </li>
        @endif

        @if(auth()->user()->role_id != 4 ||
            in_array('profile', session('available_features', [])) ||
            in_array('design', session('available_features', [])) ||
            in_array('about', session('available_features', [])) ||
            in_array('skills', session('available_features', [])) ||
            in_array('education', session('available_features', [])) ||
            in_array('experience', session('available_features', [])) ||
            in_array('categories', session('available_features', [])) ||
            in_array('portfolio', session('available_features', [])) ||
            in_array('socials', session('available_features', [])) ||
            in_array('messages', session('available_features', [])))
        <li class="{{ request()->routeIs(['design.index', 'about.index', 'skills.index', 'education.index', 'experience.index', 'categories.index', 'portfolio.index', 'socials.index', 'messages.index']) ? 'active-page' : '' }}">
            <a href="#"><i data-feather="layout" class="nav-icon"></i>
                <span>{{ __('Profile') }}</span>
                <i class="fas fa-chevron-right dropdown-icon"></i>
            </a>
            <ul>
                @if(auth()->user()->role_id != 4 || in_array('design', session('available_features', [])))
                    @can('viewAny', App\Models\Design::class)
                    <li class="{{ request()->routeIs('design.index') ? 'active-page' : '' }}">
                        <a href="{{ route('design.index') }}" class="{{ request()->routeIs('design.index') ? 'active' : '' }}">
                            <i data-feather="monitor" class="nav-icon"></i>
                            <span>{{ __('Design') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('about', session('available_features', [])))
                    @can('viewAny', App\Models\About::class)
                    <li class="{{ request()->routeIs('about.index') ? 'active-page' : '' }}">
                        <a href="{{ route('about.index') }}" class="{{ request()->routeIs('about.index') ? 'active' : '' }}">
                            <i data-feather="alert-circle" class="nav-icon"></i>
                            <span>{{ __('about') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('skills', session('available_features', [])))
                    @can('viewAny', App\Models\Skill::class)
                    <li class="{{ request()->routeIs('skills.index') ? 'active-page' : '' }}">
                        <a href="{{ route('skills.index') }}" class="{{ request()->routeIs('skills.index') ? 'active' : '' }}">
                            <i data-feather="aperture" class="nav-icon"></i>
                            <span>{{ __('skills') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('education', session('available_features', [])))
                    @can('viewAny', App\Models\Education::class)
                    <li class="{{ request()->routeIs('education.index') ? 'active-page' : '' }}">
                        <a href="{{ route('education.index') }}" class="{{ request()->routeIs('education.index') ? 'active' : '' }}">
                            <i data-feather="award" class="nav-icon"></i>
                            <span>{{ __('education') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('experience', session('available_features', [])))
                    @can('viewAny', App\Models\Experience::class)
                    <li class="{{ request()->routeIs('experience.index') ? 'active-page' : '' }}">
                        <a href="{{ route('experience.index') }}" class="{{ request()->routeIs('experience.index') ? 'active' : '' }}">
                            <i data-feather="briefcase" class="nav-icon"></i>
                            <span>{{ __('experience') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('categories', session('available_features', [])))
                    @can('viewAny', App\Models\Category::class)
                    <li class="{{ request()->routeIs('categories.index') ? 'active-page' : '' }}">
                        <a href="{{ route('categories.index') }}" class="{{ request()->routeIs('categories.index') ? 'active' : '' }}">
                            <i data-feather="tag" class="nav-icon"></i>
                            <span>{{ __('categories') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('portfolio', session('available_features', [])))
                    @can('viewAny', App\Models\Portfolio::class)
                    <li class="{{ request()->routeIs('portfolio.index') ? 'active-page' : '' }}">
                        <a href="{{ route('portfolio.index') }}" class="{{ request()->routeIs('portfolio.index') ? 'active' : '' }}">
                            <i data-feather="archive" class="nav-icon"></i>
                            <span>{{ __('portfolio') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('socials', session('available_features', [])))
                    @can('viewAny', App\Models\Social::class)
                    <li class="{{ request()->routeIs('socials.index') ? 'active-page' : '' }}">
                        <a href="{{ route('socials.index') }}" class="{{ request()->routeIs('socials.index') ? 'active' : '' }}">
                            <i data-feather="flag" class="nav-icon"></i>
                            <span>{{ __('Socials') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif

                @if(auth()->user()->role_id != 4 || in_array('messages', session('available_features', [])))
                    @can('viewAny', App\Models\Message::class)
                    <li class="{{ request()->routeIs('messages.index') ? 'active-page' : '' }}">
                        <a href="{{ route('messages.index') }}" class="{{ request()->routeIs('messages.index') ? 'active' : '' }}">
                            <i data-feather="mail" class="nav-icon"></i>
                            <span>{{ __('messages') }}</span>
                        </a>
                    </li>
                    @endcan
                @endif
            </ul>
        </li>
        @endif

        @if(auth()->user()->role_id != 4 || in_array('collection-builder', session('available_features', [])))
            @can('viewAny', App\Models\Collection::class)
            <li class="{{ request()->routeIs(['collections.index']) ? 'active-page' : '' }}">
                <a href="#"><i data-feather="server" class="nav-icon"></i>
                    <span>{{ __('Tools') }}</span>
                    <i class="fas fa-chevron-right dropdown-icon"></i>
                </a>
                <ul>
                    <li class="{{ request()->routeIs('collections.index') ? 'active-page' : '' }}">
                        <a href="{{ route('collections.index') }}" class="{{ request()->routeIs('collections.index') ? 'active' : '' }}">
                            <i data-feather="code" class="nav-icon"></i>
                            <span>{{ __('Collection Builder') }}</span>
                        </a>
                    </li>
                </ul>
            </li>
            @endcan
        @endif

        <!-- Payment History for Users -->
        <li class="{{ request()->routeIs('payments.history') ? 'active-page' : '' }}">
            <a href="{{ route('payments.history') }}" class="{{ request()->routeIs('payments.history') ? 'active' : '' }}">
                <i data-feather="credit-card" class="nav-icon"></i>
                <span>{{ __('Payment History') }}</span>
            </a>
        </li>
    </ul>
</div>
