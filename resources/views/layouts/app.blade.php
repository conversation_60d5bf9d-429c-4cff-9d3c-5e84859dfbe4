<!DOCTYPE html>
<html lang="{{ $languageShort }}" dir="{{ $languageDir }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- SEO Meta Tags from Settings --}}
    <title>{{ $meta_title ?? $site_name ?? config('app.name', 'Laravel') }}</title>
    <meta name="description" content="{{ $meta_description ?? $site_description }}" />
    <meta name="keywords" content="{{ $meta_keywords }}" />
    <meta name="author" content="{{ $site_name }}" />

    {{-- Open Graph Meta Tags --}}
    <meta property="og:title" content="{{ $meta_title ?? $site_name }}">
    <meta property="og:description" content="{{ $meta_description ?? $site_description }}">
    <meta property="og:image" content="{{ $og_image }}">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ $site_name }}">

    {{-- Twitter Card Meta Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $meta_title ?? $site_name }}">
    <meta name="twitter:description" content="{{ $meta_description ?? $site_description }}">
    <meta name="twitter:image" content="{{ $og_image }}">

    {{-- Additional Meta Tags --}}
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ $site_icon }}">
    <link rel="icon" type="image/x-icon" href="{{ $site_icon }}">
    <link rel="apple-touch-icon" href="{{ $site_icon }}">

    <link href="" rel="stylesheet" type="text/css" />

    <link rel="shortcut icon" href="{{ asset('assets/dashboard/v1/images/favicon.ico') }}">
    <!-- plugin css -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;600;700&display=swap">
    <!-- Styles -->
    <link href="{{ asset('assets/dashboard/v1/plugins/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/plugins/font-awesome/css/all.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/plugins/perfectscroll/perfect-scrollbar.css') }}" rel="stylesheet">
    <!-- Theme Styles -->
    <link href="{{ asset('assets/dashboard/v1/css/main.min.css') }}" rel="stylesheet">
    {{-- <link href="{{ asset('assets/dashboard/v1/css/toastr.min.css') }}" rel="stylesheet"> --}}
    {{-- <link href="{{ asset('assets/dashboard/v1/css/page-builder.css') }}" rel="stylesheet"> --}}
    <link href="{{ asset('assets/dashboard/v1/css/custom.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/css/hide-sidebar.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/css/image-upload.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/css/subscription-alerts.css') }}" rel="stylesheet">
    @if ($languageDir == 'rtl')
        <link href="{{ asset('assets/dashboard/v1/css/custom-rtl.css') }}" rel="stylesheet">
    @endif
    <!-- Styles -->
    {{-- <link rel="stylesheet" href="{{ mix('css/app.css') }}"> --}}
    <!-- Styles -->
    <style>
        /* for all directions */
        .page-header .navbar .navbar-brand {
            margin-right: 0;
            background: url({{ $site_logo }}) center center no-repeat;
            background-size: cover;
            width: 200px;
            height: 40px;
            margin: 0 auto;
        }

        /* Styles for when sidebar is hidden */
        .page-container.no-sidebar .page-content.full-width {
            margin-left: 0 !important;
            width: 100% !important;
        }

        /* Hide sidebar completely */
        .page-sidebar {
            transition: all 0.3s ease;
        }

        .page-container.no-sidebar .page-sidebar {
            display: none !important;
            width: 0 !important;
            overflow: hidden !important;
        }

        /* RTL support for no sidebar */
        html[dir="rtl"] .page-container.no-sidebar .page-content.full-width {
            margin-right: 0 !important;
            margin-left: 0 !important;
        }

        /* Hide sidebar toggle button when sidebar is hidden */
        .page-container.no-sidebar #sidebar-toggle {
            display: none !important;
        }
    </style>
    @livewireStyles

    {{-- Google Analytics --}}
    @if($google_analytics_id)
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ $google_analytics_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ $google_analytics_id }}');
    </script>
    @endif

    {{-- Google Tag Manager --}}
    @if($google_tag_manager_id)
    <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ $google_tag_manager_id }}');
    </script>
    @endif

    {{-- Facebook Pixel --}}
    @if($facebook_pixel_id)
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{ $facebook_pixel_id }}');
        fbq('track', 'PageView');
    </script>
    <noscript>
        <img height="1" width="1" style="display:none"
             src="https://www.facebook.com/tr?id={{ $facebook_pixel_id }}&ev=PageView&noscript=1"/>
    </noscript>
    @endif

    {{-- Custom Head Code --}}
    @if($custom_head_code)
        {!! $custom_head_code !!}
    @endif

    {{-- Rich Snippets / Structured Data --}}
    <x-site-seo />
</head>

<body class="{{ auth()->user()->role_id == 4 ? 'sidebar-hidden' : '' }}">
    {{-- Google Tag Manager (noscript) --}}
    @if($google_tag_manager_id)
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ $google_tag_manager_id }}"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    @endif

    {{-- Custom Body Code --}}
    @if($custom_body_code)
        {!! $custom_body_code !!}
    @endif

    {{-- <div class='loader'>
        <div class='spinner-grow text-primary' role='status'>
            <span class='sr-only'>{{ __('.Loading.') }}</span>
        </div>
    </div> --}}


    <div class="page-container {{ session('hide_sidebar') ? 'no-sidebar' : '' }}">
        @include('partials.navbar')
        {{-- @if (!session('hide_sidebar'))
            @include('partials.sidebar')
        @endif --}}
        @if (auth()->user()->role_id != 4)
            <style>
                body.sidebar-hidden #sidebar-toggle {
                    display: block !important;
                }
            </style>
            @include('partials.sidebar')
        @endif

        <div class="page-content {{ session('hide_sidebar') ? 'full-width' : '' }}">
            <div class="main-wrapper">
                @if (session('hide_sidebar') && auth()->user()->role_id == 4)
                    <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                        <strong>{{ __('Limited Access') }}:</strong>
                        {{ __('Subscribe to a plan to unlock all features and sidebar navigation.') }}
                        <a href="{{ route('dashboard') }}#plans" class="alert-link">{{ __('View Plans') }}</a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
                {{ $slot }}
            </div>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    @stack('modals')
    <script src="{{ asset('assets/dashboard/v1/plugins/jquery/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/popper.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/plugins/bootstrap/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/feather.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/sweetalert.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/plugins/perfectscroll/perfect-scrollbar.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/main.min.js') }}"></script>
    {{-- <script src="{{ asset('assets/dashboard/v1/js/page-builder.js') }}"></script> --}}
    @stack('js')
    @livewireScripts
    {{-- @livewireScriptConfig --}}
    <script src="{{ asset('assets/dashboard/v1/js/sweetalert.min.js') }}"></script>
    {{-- <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script> --}}
    <script src="{{ asset('assets/dashboard/v1/js/subscription-alerts.js') }}"></script>
    <x-livewire-alert::scripts />

    @if (session('hide_sidebar'))
        <script>
            // إضافة سكريبت إضافي للتأكد من إخفاء السايد بار
            document.addEventListener('DOMContentLoaded', function() {
                // إخفاء السايد بار بشكل مباشر
                var sidebar = document.querySelector('.page-sidebar');
                if (sidebar) {
                    sidebar.style.display = 'none';
                    sidebar.style.width = '0';
                    sidebar.style.overflow = 'hidden';
                }

                // التأكد من أن المحتوى يأخذ العرض الكامل
                var pageContent = document.querySelector('.page-content');
                if (pageContent) {
                    pageContent.style.marginLeft = '0';
                    pageContent.style.marginRight = '0';
                    pageContent.style.width = '100%';
                }

                // إضافة كلاس للجسم للتأكد من تطبيق التغييرات
                document.body.classList.add('sidebar-hidden');
            });
        </script>
    @endif
</body>

</html>
