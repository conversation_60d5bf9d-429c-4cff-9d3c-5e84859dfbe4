<!doctype html>
<html lang="en-US">

<head>
    <!-- Meta -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta name="description" content="{{ $description }}">
    <meta name="keywords" content="{{ $keywords }}">
    <meta name="author" content="{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}">
    <meta property="og:title" content="{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:image" content="{{ $profile->getFirstMediaUrl('avatar') ?: asset('assets/images/default-avatar.png') }}" />
    <meta property="og:url" content="{{ request()->url() }}" />
    <link rel="icon" type="image/png"
        href="https://3bdulrahman.com/storage/images/9Ov06N3J8gRjHfWc3fJvapYc77xLl8yeZ00QbHWE.png">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}</title>

    <!-- Fonts -->
    <link
        href="https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Mr+Dafoe&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('assets/v1/css/basic.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/basic.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/layout.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/magnific-popup.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/jarallax.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/owl.carousel.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/swiper.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/v1/css/fontawesome.css') }}" />

    <!-- Theme Colors
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/blue.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/green.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/orange.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/brown.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/purple.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/red.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/beige.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/yellow.css') }}" />
 <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/yellow_light.css') }}" />
 -->
    <link rel="stylesheet" href="{{ asset('assets/v1/css/theme-colors/blue.css') }}" />

    <!--[if lt IE 9]>
 <script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
 <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
 <![endif]-->

    <link rel="shortcut icon" href="{{ $site_logo }}">
    <script type="application/ld+json">
        {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "AbdulRahman",
      "alternateName": ["عبدالرحمن محيسن محمد حسن", "AbdulRahman Mohisen Mohammed Hassan"],
      "url": "https://3bdulrahman.com/",
      "image": "https://3bdulrahman.com/storage/images/9Ov06N3J8gRjHfWc3fJvapYc77xLl8yeZ00QbHWE.png",
      "jobTitle": "Full-Stack Web Developer",
      "worksFor": {
        "@type": "Organization",
        "name": "Freelancer"
      },
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Asyut",
        "addressCountry": "Egypt"
      },
      "email": "<EMAIL>",
      "telephone": "+201020904386",
      "sameAs": [
        "https://www.facebook.com/programmer.abu.hanen",
        "https://x.com/abuhanen4u",
        "https://www.linkedin.com/in/3bdulrahman",
        "https://github.com/Dev-3bdulrahman"
      ],
      "alumniOf": [
        {
          "@type": "EducationalOrganization",
          "name": "Abnoub Al-Azhari Institute"
        },
        {
          "@type": "EducationalOrganization",
          "name": "Arab African Institute"
        },
        {
          "@type": "EducationalOrganization",
          "name": "Computer Science and Business Administration Center at Assiut University"
        }
      ],
      "skills": ["Graphic Design", "Fullstack Developer","Backend Development", "Web Application Development", "Information Systems Analysis", "Database Analysis", "Problem Solving"],
      "knowsLanguage": [
        {
          "@type": "Language",
          "name": "Arabic"
        },
        {
          "@type": "Language",
          "name": "English"
        }
      ],
      "birthPlace": {
        "@type": "Place",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Asyut",
          "addressCountry": "Egypt"
        }
      },
      "birthDate": "1989-02-18",
      "knowsAbout": [
        "Graphic Design",
        "Frontend Development",
        "Web Application Development",
        "Information Systems Analysis",
        "Database Analysis",
        "Problem Solving"
      ],
      "hasOccupation": [
        {
          "@type": "Occupation",
          "name": "Web Applications Developer",
          "description": "Building high-performance web applications using Laravel, Livewire, and modern frontend technologies. Experienced in developing scalable, secure, and efficient solutions, integrating APIs, and optimizing database performance to enhance user experience and functionality.",
          "estimatedSalary": [
            {
              "@type": "MonetaryAmountDistribution",
              "name": "base",
              "currency": "USD",
              "duration": "P1Y",
              "percentile10": 40000,
              "percentile25": 45000,
              "median": 50000,
              "percentile75": 55000,
              "percentile90": 60000
            }
          ],
          "occupationLocation": [
            {
              "@type": "Country",
              "name": "Egypt"
            }
          ]
        },
        {
          "@type": "Occupation",
          "name": "WordPress Themes & Plugins Developer",
          "description": "Specialized in developing custom WordPress themes and plugins, ensuring high performance, security, and seamless user experience. Proficient in optimizing WordPress sites for speed, SEO, and functionality, while integrating advanced features tailored to business needs.",
          "estimatedSalary": [
            {
              "@type": "MonetaryAmountDistribution",
              "name": "base",
              "currency": "USD",
              "duration": "P1Y",
              "percentile10": 30000,
              "percentile25": 35000,
              "median": 40000,
              "percentile75": 45000,
              "percentile90": 50000
            }
          ],
          "occupationLocation": [
            {
              "@type": "Country",
              "name": "United Arab Emirates"
            }
          ]
        }
      ]
    }
    </script>
    @livewireStyles
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-S8WG4K7SER"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-S8WG4K7SER');
    </script>
</head>

<body class="home">

    <!-- Preloader -->
    <div class="preloader">
        <div class="box-1">
            <div class="centrize full-width">
                <div class="vertical-center">
                    <div class="spinner">
                        <div class="lines"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="box-2"></div>
    </div>
    <!-- Container -->
    <div class="container">
        {{ $slot }}
    </div>
    <!-- Scripts -->
    <script src="{{ asset('assets/v1/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/v1/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/v1/js/velocity.min.js') }}"></script>
    <script src="{{ asset('assets/v1/js/jquery.validate.js') }}"></script>
    <script src="{{ asset('assets/v1/js/magnific-popup.js') }}"></script>
    <script src="{{ asset('assets/v1/js/typed.js') }}"></script>
    <script src="{{ asset('assets/v1/js/jarallax.js') }}"></script>
    <script src="{{ asset('assets/v1/js/jarallax-video.js') }}"></script>
    <script src="{{ asset('assets/v1/js/jarallax-element.js') }}"></script>
    <script src="{{ asset('assets/v1/js/imagesloaded.pkgd.js') }}"></script>
    <script src="{{ asset('assets/v1/js/isotope.pkgd.js') }}"></script>
    <script src="{{ asset('assets/v1/js/owl.carousel.js') }}"></script>
    <script src="{{ asset('assets/v1/js/swiper.js') }}"></script>
    <script src="{{ asset('assets/v1/js/scripts.js') }}"></script>
    @livewireScripts
    <script src="{{ asset('assets/js/sweetalert.min.js') }}"></script>
    {{-- <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script> --}}
    <x-livewire-alert::scripts />
</body>

</html>
