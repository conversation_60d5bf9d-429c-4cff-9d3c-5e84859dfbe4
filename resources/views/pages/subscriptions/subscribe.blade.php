<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            @if($successMessage)
                <div class="alert alert-success">
                    {{ $successMessage }}
                    <div class="text-center mt-3" wire:poll="redirectToDashboard">  
                        <div class="spinner-border spinner-border-sm text-success me-2" role="status">
                            <span class="visually-hidden">{{ __('Loading') }}...</span>
                        </div>
                        {{ __('Redirecting to dashboard') }}...
                    </div>
                </div>
            @endif
            
            @if($errorMessage)
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ $errorMessage }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
            
            @if(!$successMessage)
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white py-3">
                        <h4 class="mb-0">{{ __('Confirm Subscription') }}</h4>
                    </div>
                    
                    <div class="card-body">
                        @if($plan)
                            <div class="text-center mb-4">
                                <div class="d-inline-block rounded-circle bg-primary text-white p-4 mb-3">
                                    <i class="fas fa-crown fa-2x"></i>
                                </div>
                                <h3>{{ $plan->getTranslation('name', app()->getLocale()) }}</h3>
                                <div class="mb-3">
                                    <span class="fs-3 fw-bold">{{ number_format($plan->price, 2) }}</span>
                                    <span class="text-muted">/ {{ $plan->billing_interval_count }} {{ __($plan->billing_interval) }}</span>
                                </div>
                            </div>
                            
                            <div class="border-top border-bottom py-4 mb-4">
                                <h5 class="mb-3">{{ __('Features Included') }}</h5>
                                <ul class="list-unstyled">
                                    @forelse($plan->features as $feature)
                                        @php
                                            $limitType = $feature->pivot->limit_type_id ? \App\Models\LimitType::find($feature->pivot->limit_type_id) : null;
                                            $limitName = $limitType ? ($limitType->name[app()->getLocale()] ?? $limitType->name['en'] ?? '') : '';
                                        @endphp
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            {{ $feature->getTranslation('name', app()->getLocale()) }}
                                            @if($feature->pivot->allowed_limit)
                                                <span class="text-muted">- {{ $feature->pivot->allowed_limit }} {{ $limitName }}</span>
                                            @endif
                                        </li>
                                    @empty
                                        <li>{{ __('No features available') }}</li>
                                    @endforelse
                                </ul>
                            </div>
                            
                            @if($plan->price > 0)
                                @if(count($availableGateways) > 0)
                                    <div class="alert alert-info mb-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        {{ __('Choose your preferred payment method below. All transactions are secure.') }}
                                    </div>
                                    
                                    <div class="mb-4">
                                        <h6 class="mb-3">{{ __('Available Payment Methods') }}</h6>
                                        <div class="d-flex flex-wrap justify-content-center gap-2">
                                            @foreach($availableGateways as $key => $gateway)
                                                <button type="button" class="btn btn-outline-primary py-3 px-4 payment-gateway-btn" wire:click="selectGateway('{{ $key }}')" wire:loading.attr="disabled" wire:target="selectGateway('{{ $key }}')">
                                                    <span wire:loading.remove wire:target="selectGateway('{{ $key }}')">
                                                        <i class="{{ $gateway['icon'] }} me-2"></i>
                                                        {{ __('Pay with') }} {{ $gateway['name'] }}
                                                    </span>
                                                    <span wire:loading wire:target="selectGateway('{{ $key }}')">
                                                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                        {{ __('Processing') }}...
                                                    </span>
                                                </button>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="alert alert-warning mb-4">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        {{ __('No payment methods are currently available. Please contact support.') }}
                                    </div>
                                @endif
                            @endif
                            
                            <p class="mb-4">
                                {{ __('By subscribing, you agree to our terms and conditions') }}
                            </p>
                            
                            <div class="d-grid gap-2">
                                @if($plan->price == 0)
                                    <button type="button" class="btn btn-primary py-3" wire:click="subscribe" wire:loading.attr="disabled">
                                        <span wire:loading.remove wire:target="subscribe">
                                            {{ __('Confirm Subscription') }}
                                        </span>
                                        <span wire:loading wire:target="subscribe">
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            {{ __('Processing') }}...
                                        </span>
                                    </button>
                                @endif
                                <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary py-3">{{ __('Cancel') }}</a>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ __('Plan not found or not specified. Please select a plan from the dashboard') }}
                            </div>
                            <div class="text-center">
                                <a href="{{ route('dashboard') }}" class="btn btn-primary">{{ __('Back to Dashboard') }}</a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
.payment-gateway-btn {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.payment-gateway-btn:hover {
    border-color: #0d6efd;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-2px);
}

.payment-gateway-btn:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.payment-gateway-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.payment-gateway-btn .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

@media (max-width: 576px) {
    .payment-gateway-btn {
        font-size: 0.9rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .payment-gateway-btn i {
        font-size: 1.1rem;
    }
    
    .d-flex.flex-wrap {
        flex-direction: column;
    }
}

/* تحسين الأزرار عند التحميل */
.payment-gateway-btn[wire\:loading\.attr="disabled"] {
    pointer-events: none;
}

/* تحسين مظهر الأيقونات */
.payment-gateway-btn i {
    font-size: 1.2rem;
    vertical-align: middle;
}
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('redirectToDashboard', () => {
            setTimeout(() => {
                window.location.href = '{{ route("dashboard") }}';
            }, 2000);
        });
        
        // Payment gateway selection
        document.querySelectorAll('.payment-gateway').forEach(gateway => {
            gateway.addEventListener('click', function() {
                document.querySelectorAll('.payment-gateway').forEach(g => g.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
    });
</script>
@endpush
