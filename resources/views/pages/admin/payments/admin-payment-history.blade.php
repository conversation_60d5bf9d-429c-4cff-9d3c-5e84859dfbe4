<div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">إدارة المدفوعات</h4>
                    <p class="text-muted">إدارة جميع المدفوعات في المنصة</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات عامة -->
        <div class="row mb-4">
            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-primary text-primary rounded">
                                        <i class="mdi mdi-credit-card-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">إجمالي المدفوعات</p>
                                <h5 class="mb-0">{{ $stats['total_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-success text-success rounded">
                                        <i class="mdi mdi-check-circle-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">مكتملة</p>
                                <h5 class="mb-0">{{ $stats['completed_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-info text-info rounded">
                                        <i class="mdi mdi-currency-usd font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">إجمالي الإيرادات</p>
                                <h5 class="mb-0">${{ number_format($stats['total_amount'] ?? 0, 2) }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-warning text-warning rounded">
                                        <i class="mdi mdi-clock-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">معلقة</p>
                                <h5 class="mb-0">{{ $stats['pending_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-danger text-danger rounded">
                                        <i class="mdi mdi-close-circle-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">فاشلة</p>
                                <h5 class="mb-0">{{ $stats['failed_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-secondary text-secondary rounded">
                                        <i class="mdi mdi-account-group font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">المستخدمون</p>
                                <h5 class="mb-0">{{ $stats['total_users'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select wire:model="filters.status" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="pending">معلقة</option>
                                    <option value="failed">فاشلة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">بوابة الدفع</label>
                                <select wire:model="filters.gateway" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="paypal">PayPal</option>
                                    <option value="stripe">Stripe</option>
                                    <option value="paymob">Paymob</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المستخدم</label>
                                <select wire:model="filters.user_id" class="form-select">
                                    <option value="">جميع المستخدمين</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">البحث</label>
                                <input type="text" wire:model="filters.search" class="form-control" placeholder="الاسم أو البريد الإلكتروني">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">من</label>
                                <input type="date" wire:model="filters.date_from" class="form-control">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">إلى</label>
                                <input type="date" wire:model="filters.date_to" class="form-control">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button wire:click="applyFilters" class="btn btn-primary btn-sm">تصفية</button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <button wire:click="resetFilters" class="btn btn-secondary btn-sm">إعادة تعيين الفلاتر</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المدفوعات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-centered table-nowrap mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الدفعة</th>
                                        <th>المستخدم</th>
                                        <th>الخطة</th>
                                        <th>المبلغ</th>
                                        <th>بوابة الدفع</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if($this->payments && $this->payments->count() > 0)
                                        @foreach($this->payments as $payment)
                                            <tr>
                                                <td>
                                                    <span class="text-muted">#{{ substr($payment->payment_id, 0, 10) }}...</span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>{{ $payment->user->name }}</strong>
                                                        <small class="text-muted d-block">{{ $payment->user->email }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($payment->plan)
                                                        {{ $payment->plan->name }}
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <strong>${{ number_format($payment->amount, 2) }}</strong>
                                                    <small class="text-muted d-block">{{ $payment->currency }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark">{{ $payment->gateway_name }}</span>
                                                </td>
                                                <td>{!! $payment->status_badge !!}</td>
                                                <td>
                                                    {{ $payment->created_at->format('Y-m-d H:i') }}
                                                    @if($payment->paid_at)
                                                        <small class="text-muted d-block">تم الدفع: {{ $payment->paid_at->format('Y-m-d H:i') }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button wire:click="showPaymentDetails('{{ $payment->payment_id }}')" class="btn btn-sm btn-outline-primary">
                                                        عرض التفاصيل
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="mdi mdi-credit-card-off font-size-48 text-muted mb-2"></i>
                                                    <p class="text-muted">لا توجد مدفوعات</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>

                        @if($this->payments && $this->payments->hasPages())
                            <div class="row mt-4">
                                <div class="col-12">
                                    {{ $this->payments->links() }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل الدفع -->
    @if($showDetails && $selectedPayment)
        <div class="modal fade show" style="display: block;" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الدفعة - #{{ substr($selectedPayment->payment_id, 0, 15) }}</h5>
                        <button type="button" class="btn-close" wire:click="closeDetails"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الدفعة</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>رقم الدفعة:</strong></td>
                                        <td>{{ $selectedPayment->payment_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المبلغ:</strong></td>
                                        <td>${{ number_format($selectedPayment->amount, 2) }} {{ $selectedPayment->currency }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>بوابة الدفع:</strong></td>
                                        <td>{{ $selectedPayment->gateway_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>{!! $selectedPayment->status_badge !!}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>النوع:</strong></td>
                                        <td>{{ ucfirst($selectedPayment->type) }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات المستخدم</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>الاسم:</strong></td>
                                        <td>{{ $selectedPayment->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>البريد الإلكتروني:</strong></td>
                                        <td>{{ $selectedPayment->user->email }}</td>
                                    </tr>
                                </table>
                                
                                <h6>التواريخ</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>تاريخ الإنشاء:</strong></td>
                                        <td>{{ $selectedPayment->created_at->format('Y-m-d H:i:s') }}</td>
                                    </tr>
                                    @if($selectedPayment->paid_at)
                                        <tr>
                                            <td><strong>تاريخ الدفع:</strong></td>
                                            <td>{{ $selectedPayment->paid_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                        
                        @if($selectedPayment->plan)
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>معلومات الخطة</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم الخطة:</strong></td>
                                            <td>{{ $selectedPayment->plan->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>سعر الخطة:</strong></td>
                                            <td>${{ number_format($selectedPayment->plan->price, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        @endif

                        @if($selectedPayment->gateway_data)
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>بيانات بوابة الدفع</h6>
                                    <div class="bg-light p-3 rounded">
                                        <pre class="mb-0" style="max-height: 200px; overflow-y: auto;">{{ json_encode($selectedPayment->gateway_data, JSON_PRETTY_PRINT) }}</pre>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeDetails">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    @endif
</div>