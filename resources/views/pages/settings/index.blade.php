<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ __('System Settings') }}</h4>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'basic' ? 'active' : '' }}" 
                               wire:click="setActiveTab('basic')" href="#" role="tab">
                                {{ __('Basic Information') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'localization' ? 'active' : '' }}" 
                               wire:click="setActiveTab('localization')" href="#" role="tab">
                                {{ __('Localization') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'contact' ? 'active' : '' }}" 
                               wire:click="setActiveTab('contact')" href="#" role="tab">
                                {{ __('Contact Information') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'theme' ? 'active' : '' }}" 
                               wire:click="setActiveTab('theme')" href="#" role="tab">
                                {{ __('Theme & Appearance') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'media' ? 'active' : '' }}" 
                               wire:click="setActiveTab('media')" href="#" role="tab">
                                {{ __('Images & Media') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'email' ? 'active' : '' }}" 
                               wire:click="setActiveTab('email')" href="#" role="tab">
                                {{ __('Email Settings') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'seo' ? 'active' : '' }}" 
                               wire:click="setActiveTab('seo')" href="#" role="tab">
                                {{ __('SEO Settings') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'rich_snippets' ? 'active' : '' }}" 
                               wire:click="setActiveTab('rich_snippets')" href="#" role="tab">
                                {{ __('Rich Snippets') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'integrations' ? 'active' : '' }}" 
                               wire:click="setActiveTab('integrations')" href="#" role="tab">
                                {{ __('Integrations') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'payments' ? 'active' : '' }}" 
                               wire:click="setActiveTab('payments')" href="#" role="tab">
                                {{ __('Payment Settings') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'performance' ? 'active' : '' }}" 
                               wire:click="setActiveTab('performance')" href="#" role="tab">
                                {{ __('Performance') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $activeTab === 'security' ? 'active' : '' }}" 
                               wire:click="setActiveTab('security')" href="#" role="tab">
                                {{ __('Security Settings') }}
                            </a>
                        </li>
                    </ul>

                    <form wire:submit.prevent="updateSettings">
                        <!-- Basic Site Information Tab -->
                        @if($activeTab === 'basic')
                        <div class="tab-content">
                            <!-- Language Tabs for Translatable Fields -->
                            <ul class="nav nav-pills mb-3">
                                @foreach($languages as $language)
                                <li class="nav-item">
                                    <a class="nav-link {{ $activeLanguage === $language->short ? 'active' : '' }}" 
                                       wire:click="setActiveLanguage('{{ $language->short }}')" href="#">
                                        {{ $language->name }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Site Name') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" 
                                               wire:model="settings.site_name.{{ $activeLanguage }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Site URL') }}</label>
                                        <input type="url" class="form-control" wire:model="settings.site_url">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Site Description') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" 
                                                  wire:model="settings.site_description.{{ $activeLanguage }}"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Site Keywords') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="2" 
                                                  wire:model="settings.site_keywords.{{ $activeLanguage }}"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Site Tagline') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" 
                                               wire:model="settings.site_tagline.{{ $activeLanguage }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Admin Email') }}</label>
                                        <input type="email" class="form-control" wire:model="settings.admin_email">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Support Email') }}</label>
                                        <input type="email" class="form-control" wire:model="settings.support_email">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('No Reply Email') }}</label>
                                        <input type="email" class="form-control" wire:model="settings.noreply_email">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Localization Settings Tab -->
                        @if($activeTab === 'localization')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Default Language') }}</label>
                                        <select class="form-control" wire:model="settings.default_language">
                                            @foreach($languages as $language)
                                                <option value="{{ $language->id }}">{{ $language->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Timezone') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.timezone" 
                                               placeholder="{{ __('e.g., Asia/Dubai') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Date Format') }}</label>
                                        <select class="form-control" wire:model="settings.date_format">
                                            <option value="Y-m-d">{{ __('YYYY-MM-DD') }}</option>
                                            <option value="d/m/Y">{{ __('DD/MM/YYYY') }}</option>
                                            <option value="m/d/Y">{{ __('MM/DD/YYYY') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Time Format') }}</label>
                                        <select class="form-control" wire:model="settings.time_format">
                                            <option value="H:i">{{ __('24 Hour (HH:MM)') }}</option>
                                            <option value="h:i A">{{ __('12 Hour (HH:MM AM/PM)') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Currency') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.currency" 
                                               placeholder="{{ __('e.g., USD') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Currency Symbol') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.currency_symbol" 
                                               placeholder="{{ __('e.g., $') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Currency Position') }}</label>
                                        <select class="form-control" wire:model="settings.currency_position">
                                            <option value="before">{{ __('Before Amount ($100)') }}</option>
                                            <option value="after">{{ __('After Amount (100$)') }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Contact Information Tab -->
                        @if($activeTab === 'contact')
                        <div class="tab-content">
                            <!-- Language Tabs for Contact Address -->
                            <ul class="nav nav-pills mb-3">
                                @foreach($languages as $language)
                                <li class="nav-item">
                                    <a class="nav-link {{ $activeLanguage === $language->short ? 'active' : '' }}" 
                                       wire:click="setActiveLanguage('{{ $language->short }}')" href="#">
                                        {{ $language->name }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Contact Phone') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.contact_phone" 
                                               placeholder="{{ __('e.g., +971501234567') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('WhatsApp Number') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.contact_whatsapp" 
                                               placeholder="{{ __('e.g., +971501234567') }}">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Contact Address') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" 
                                                  wire:model="settings.contact_address.{{ $activeLanguage }}" 
                                                  placeholder="{{ __('Enter your business address') }}"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Latitude') }}</label>
                                        <input type="number" step="any" class="form-control" wire:model="settings.latitude" 
                                               placeholder="{{ __('e.g., 25.2048') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Longitude') }}</label>
                                        <input type="number" step="any" class="form-control" wire:model="settings.longitude" 
                                               placeholder="{{ __('e.g., 55.2708') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Theme Settings Tab -->
                        @if($activeTab === 'theme')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Default Theme') }}</label>
                                        <select class="form-control" wire:model="settings.default_theme">
                                            <option value="light">{{ __('Light Theme') }}</option>
                                            <option value="dark">{{ __('Dark Theme') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Allow Dark Mode Toggle') }}</label>
                                        <select class="form-control" wire:model="settings.dark_mode_enabled">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Font Family') }}</label>
                                        <select class="form-control" wire:model="settings.font_family">
                                            <option value="Inter">{{ __('Inter') }}</option>
                                            <option value="Roboto">{{ __('Roboto') }}</option>
                                            <option value="Open Sans">{{ __('Open Sans') }}</option>
                                            <option value="Lato">{{ __('Lato') }}</option>
                                            <option value="Poppins">{{ __('Poppins') }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Security Settings Tab -->
                        @if($activeTab === 'security')
                        <div class="tab-content">
                            <!-- Language Tabs for Maintenance Message -->
                            <ul class="nav nav-pills mb-3">
                                @foreach($languages as $language)
                                <li class="nav-item">
                                    <a class="nav-link {{ $activeLanguage === $language->short ? 'active' : '' }}" 
                                       wire:click="setActiveLanguage('{{ $language->short }}')" href="#">
                                        {{ $language->name }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>

                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('User Registration & Authentication') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('User Registration') }}</label>
                                        <select class="form-control" wire:model="settings.user_registration">
                                            <option value="1">{{ __('Allow Registration') }}</option>
                                            <option value="0">{{ __('Disable Registration') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Email Verification') }}</label>
                                        <select class="form-control" wire:model="settings.email_verification">
                                            <option value="1">{{ __('Required') }}</option>
                                            <option value="0">{{ __('Optional') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Two Factor Authentication') }}</label>
                                        <select class="form-control" wire:model="settings.two_factor_auth">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Session Lifetime') }} ({{ __('minutes') }})</label>
                                        <input type="number" class="form-control" wire:model="settings.session_lifetime" 
                                               placeholder="{{ __('e.g., 120') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Maximum File Size') }} ({{ __('KB') }})</label>
                                        <input type="number" class="form-control" wire:model="settings.max_file_size" 
                                               placeholder="{{ __('e.g., 2048') }}">
                                    </div>
                                </div>

                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Maintenance Mode') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Maintenance Mode Status') }}</label>
                                        <select class="form-control" wire:model="settings.maintenance_mode">
                                            <option value="0">{{ __('Disabled - Site Active') }}</option>
                                            <option value="1">{{ __('Enabled - Site Under Maintenance') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Maintenance Message') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" 
                                                  wire:model="settings.maintenance_message.{{ $activeLanguage }}" 
                                                  placeholder="{{ __('Enter maintenance message for users') }}"></textarea>
                                    </div>
                                </div>

                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Business Information') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Business Name') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" 
                                               wire:model="settings.business_name.{{ $activeLanguage }}" 
                                               placeholder="{{ __('Enter your business name') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Images & Media Tab -->
                        @if($activeTab === 'media')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('Site Logos & Favicon') }}</h6>
                                    <p class="text-muted">{{ __('Upload your brand logos for different languages and favicon') }}</p>
                                    <hr>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Default Logo') }}</label>
                                        <input type="file" class="form-control" wire:model="settings.site_logo" accept="image/*">
                                        <small class="text-muted">{{ __('Recommended size: 200x60px. Max size: 2MB') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Arabic Logo') }}</label>
                                        <input type="file" class="form-control" wire:model="site_logo_ar" accept="image/*">
                                        <small class="text-muted">{{ __('Upload Arabic Logo') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('English Logo') }}</label>
                                        <input type="file" class="form-control" wire:model="site_logo_en" accept="image/*">
                                        <small class="text-muted">{{ __('Upload English Logo') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Site Favicon') }}</label>
                                        <input type="file" class="form-control" wire:model="site_favicon" accept="image/x-icon,image/png">
                                        <small class="text-muted">{{ __('Recommended size: 32x32px. Max size: 2MB') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Social Media Images') }}</h6>
                                    <hr>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Open Graph Image') }}</label>
                                        <input type="file" class="form-control" wire:model="og_image" accept="image/*">
                                        <small class="text-muted">{{ __('Recommended size: 1200x630px. Max size: 2MB') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Email Settings Tab -->
                        @if($activeTab === 'email')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('SMTP Configuration') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Driver') }}</label>
                                        <select class="form-control" wire:model="settings.mail_driver">
                                            <option value="smtp">SMTP</option>
                                            <option value="sendmail">Sendmail</option>
                                            <option value="mailgun">Mailgun</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Host') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.mail_host" placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Port') }}</label>
                                        <input type="number" class="form-control" wire:model="settings.mail_port" placeholder="587">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Encryption') }}</label>
                                        <select class="form-control" wire:model="settings.mail_encryption">
                                            <option value="tls">TLS</option>
                                            <option value="ssl">SSL</option>
                                            <option value="">{{ __('None') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Username') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.mail_username">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Mail Password') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.mail_password">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('From Address') }}</label>
                                        <input type="email" class="form-control" wire:model="settings.mail_from_address">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('From Name') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.mail_from_name">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- SEO Settings Tab -->
                        @if($activeTab === 'seo')
                        <div class="tab-content">
                            <ul class="nav nav-pills mb-3">
                                @foreach($languages as $language)
                                <li class="nav-item">
                                    <a class="nav-link {{ $activeLanguage === $language->short ? 'active' : '' }}" 
                                       wire:click="setActiveLanguage('{{ $language->short }}')" href="#">
                                        {{ $language->name }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Meta Title') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" wire:model="settings.meta_title.{{ $activeLanguage }}">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Meta Description') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" wire:model="settings.meta_description.{{ $activeLanguage }}"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Meta Keywords') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="2" wire:model="settings.meta_keywords.{{ $activeLanguage }}"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Google Analytics ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.google_analytics_id" placeholder="G-XXXXXXXXXX">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Google Tag Manager ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.google_tag_manager_id" placeholder="GTM-XXXXXXX">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Facebook Pixel ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.facebook_pixel_id" placeholder="123456789012345">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Custom Head Code') }}</label>
                                        <small class="form-text text-muted">{{ __('HTML code to be inserted in the head section') }}</small>
                                        <textarea class="form-control" rows="4" wire:model="settings.custom_head_code" placeholder="<script>...</script>"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Custom Body Code') }}</label>
                                        <small class="form-text text-muted">{{ __('HTML code to be inserted at the beginning of the body section') }}</small>
                                        <textarea class="form-control" rows="4" wire:model="settings.custom_body_code" placeholder="<script>...</script>"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Robots.txt Content') }}</label>
                                        <small class="form-text text-muted">{{ __('Custom robots.txt content. Leave empty to use default.') }}</small>
                                        <textarea class="form-control" rows="5" wire:model="settings.robots_txt" placeholder="User-agent: *&#10;Allow: /"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Integrations Tab -->
                        @if($activeTab === 'integrations')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('reCAPTCHA') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('reCAPTCHA Site Key') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.recaptcha_site_key">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('reCAPTCHA Secret Key') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.recaptcha_secret_key">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('reCAPTCHA Version') }}</label>
                                        <select class="form-control" wire:model="settings.recaptcha_version">
                                            <option value="v2">reCAPTCHA v2</option>
                                            <option value="v3">reCAPTCHA v3</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Enable reCAPTCHA') }}</label>
                                        <select class="form-control" wire:model="settings.recaptcha_enabled">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Social Login') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Facebook App ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.facebook_app_id">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Google Client ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.google_client_id">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Performance Tab -->
                        @if($activeTab === 'performance')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('Cache Settings') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Enable Cache') }}</label>
                                        <select class="form-control" wire:model="settings.cache_enabled">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Cache Duration') }} ({{ __('seconds') }})</label>
                                        <input type="number" class="form-control" wire:model="settings.cache_duration" placeholder="3600">
                                    </div>
                                </div>
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Optimization Settings') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Image Optimization') }}</label>
                                        <select class="form-control" wire:model="settings.image_optimization">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Lazy Loading') }}</label>
                                        <select class="form-control" wire:model="settings.lazy_loading">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Minify CSS') }}</label>
                                        <select class="form-control" wire:model="settings.minify_css">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Minify JS') }}</label>
                                        <select class="form-control" wire:model="settings.minify_js">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Backup & Logs') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Auto Backup') }}</label>
                                        <select class="form-control" wire:model="settings.auto_backup">
                                            <option value="1">{{ __('Enabled') }}</option>
                                            <option value="0">{{ __('Disabled') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Backup Frequency') }}</label>
                                        <select class="form-control" wire:model="settings.backup_frequency">
                                            <option value="daily">{{ __('Daily') }}</option>
                                            <option value="weekly">{{ __('Weekly') }}</option>
                                            <option value="monthly">{{ __('Monthly') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('Log Retention') }} ({{ __('days') }})</label>
                                        <input type="number" class="form-control" wire:model="settings.log_retention_days" placeholder="30">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Rich Snippets Settings Tab -->
                        @if($activeTab === 'rich_snippets')
                        <div class="tab-content">
                            <ul class="nav nav-pills mb-3">
                                @foreach($languages as $language)
                                <li class="nav-item">
                                    <a class="nav-link {{ $activeLanguage === $language->short ? 'active' : '' }}" 
                                       wire:click="setActiveLanguage('{{ $language->short }}')" href="#">
                                        {{ $language->name }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                            
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <div class="form-group">
                                        <label class="fw-bold">{{ __('Enable Rich Snippets') }}</label>
                                        <small class="form-text text-muted d-block mb-2">{{ __('Enable structured data markup for better search engine visibility') }}</small>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enable_rich_snippets" id="rich_snippets_enabled" value="1" wire:model="settings.enable_rich_snippets">
                                            <label class="form-check-label" for="rich_snippets_enabled">{{ __('Enabled') }}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enable_rich_snippets" id="rich_snippets_disabled" value="0" wire:model="settings.enable_rich_snippets">
                                            <label class="form-check-label" for="rich_snippets_disabled">{{ __('Disabled') }}</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('Business Information') }}</h6>
                                    <hr>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Business Email') }}</label>
                                        <input type="email" class="form-control" wire:model="settings.business_email">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Business Phone') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.business_phone">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Founded Date') }}</label>
                                        <input type="date" class="form-control" wire:model="settings.business_founded_date">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Price Range') }}</label>
                                        <select class="form-control" wire:model="settings.business_price_range">
                                            <option value="">{{ __('Select Price Range') }}</option>
                                            <option value="$">$ - Budget</option>
                                            <option value="$$">$$ - Moderate</option>
                                            <option value="$$$">$$$ - Expensive</option>
                                            <option value="$$$$">$$$$ - Very Expensive</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Alternative Names') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" wire:model="settings.alternate_names.{{ $activeLanguage }}" placeholder="{{ __('Enter alternative names separated by commas') }}">
                                        <small class="form-text text-muted">{{ __('Example: شركة عبدالرحمن, AbdulRahman Company') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Business Address') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" wire:model="settings.business_address.{{ $activeLanguage }}" placeholder="{{ __('Enter complete business address') }}"></textarea>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Business Type') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" wire:model="settings.business_type.{{ $activeLanguage }}" placeholder="{{ __('e.g., Technology Company, Web Development Agency') }}">
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Services Offered') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="3" wire:model="settings.services_offered.{{ $activeLanguage }}" placeholder="{{ __('Enter services separated by commas') }}"></textarea>
                                        <small class="form-text text-muted">{{ __('Example: Web Development, Mobile Apps, SEO Services') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Skills & Expertise') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="2" wire:model="settings.business_skills.{{ $activeLanguage }}" placeholder="{{ __('Enter skills separated by commas') }}"></textarea>
                                        <small class="form-text text-muted">{{ __('Example: PHP, Laravel, JavaScript, React') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Languages Supported') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <input type="text" class="form-control" wire:model="settings.business_languages.{{ $activeLanguage }}" placeholder="{{ __('Enter languages separated by commas') }}">
                                        <small class="form-text text-muted">{{ __('Example: Arabic, English, French') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Areas of Expertise') }} ({{ strtoupper($activeLanguage) }})</label>
                                        <textarea class="form-control" rows="2" wire:model="settings.areas_of_expertise.{{ $activeLanguage }}" placeholder="{{ __('Enter areas separated by commas') }}"></textarea>
                                        <small class="form-text text-muted">{{ __('Example: E-commerce Solutions, Custom Software, Digital Marketing') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Social Media Profiles') }}</h6>
                                    <hr>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Facebook URL') }}</label>
                                        <input type="url" class="form-control" wire:model="settings.facebook_url">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Twitter URL') }}</label>
                                        <input type="url" class="form-control" wire:model="settings.twitter_url">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('LinkedIn URL') }}</label>
                                        <input type="url" class="form-control" wire:model="settings.linkedin_url">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Instagram URL') }}</label>
                                        <input type="url" class="form-control" wire:model="settings.instagram_url">
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Business Hours & Payment') }}</h6>
                                    <hr>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Opening Hours') }}</label>
                                        <textarea class="form-control" rows="4" wire:model="opening_hours_input" placeholder="{{ __('Enter opening hours (JSON format or text)') }}"></textarea>
                                        <small class="form-text text-muted">{{ __('Example: Monday-Friday: 9:00-18:00, Saturday: 9:00-14:00') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Payment Methods Accepted') }}</label>
                                        <input type="text" class="form-control" wire:model="payment_methods_input" placeholder="{{ __('Enter payment methods separated by commas') }}">
                                        <small class="form-text text-muted">{{ __('Example: Cash, Credit Card, PayPal, Bank Transfer') }}</small>
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('Custom Schema Markup') }}</h6>
                                    <hr>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Custom Schema.org JSON-LD') }}</label>
                                        <textarea class="form-control" rows="6" wire:model="custom_schema_input" placeholder="{{ __('Enter custom JSON-LD schema markup') }}"></textarea>
                                        <small class="form-text text-muted">{{ __('Advanced: Add custom structured data markup in JSON-LD format') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Payment Settings Tab -->
                        @if($activeTab === 'payments')
                        <div class="tab-content">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h6 class="text-info">{{ __('Payment Gateway Settings') }}</h6>
                                    <p class="text-muted small">{{ __('Configure your payment gateways and webhook URLs for secure payment processing') }}</p>
                                    <hr>
                                </div>
                                
                                <!-- Webhook Status Indicator -->
                                <div class="col-12 mb-4">
                                    <div class="alert alert-info d-flex align-items-center" role="alert">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <div>
                                            <strong>{{ __('Webhook URLs') }}</strong><br>
                                            <small>{{ __('Copy these URLs and configure them in your payment gateway dashboards to receive payment notifications') }}</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Stripe Settings -->
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" wire:model.boolean="settings.payment_stripe_enabled" id="stripe_enabled">
                                        <label class="form-check-label fw-bold" for="stripe_enabled">
                                            {{ __('Enable Stripe') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Stripe Public Key') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.stripe_public_key" placeholder="pk_test_...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Stripe Secret Key') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.stripe_secret_key" placeholder="sk_test_...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Stripe Webhook Secret') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.stripe_webhook_secret" placeholder="whsec_...">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Stripe Webhook URL') }}</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="{{ url('/webhooks/stripe') }}" readonly style="font-family: monospace; direction: ltr; text-align: left;">
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyWebhookUrl('{{ url('/webhooks/stripe') }}', this)">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <span class="badge {{ ($settings['payment_stripe_enabled'] ?? false) ? 'bg-success' : 'bg-secondary' }} me-1">{{ ($settings['payment_stripe_enabled'] ?? false) ? __('Active') : __('Inactive') }}</span>
                                            {{ __('Use this URL in your Stripe dashboard webhook settings') }}
                                        </small>
                                    </div>
                                </div>
                                
                                <!-- PayPal Settings -->
                                <div class="col-12 mt-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" wire:model.boolean="settings.payment_paypal_enabled" id="paypal_enabled">
                                        <label class="form-check-label fw-bold" for="paypal_enabled">
                                            {{ __('Enable PayPal') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('PayPal Client ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.paypal_client_id">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('PayPal Client Secret') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.paypal_client_secret">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" wire:model.boolean="settings.paypal_sandbox" id="paypal_sandbox">
                                            <label class="form-check-label" for="paypal_sandbox">
                                                {{ __('Enable Sandbox Mode (Test)') }}
                                            </label>
                                        </div>
                                        <small class="text-muted">{{ __('Uncheck for Live (Production) mode') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('PayPal Webhook ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.paypal_webhook_id">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('PayPal Webhook URL') }}</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="{{ url('/webhooks/paypal') }}" readonly style="font-family: monospace; direction: ltr; text-align: left;">
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyWebhookUrl('{{ url('/webhooks/paypal') }}', this)">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <span class="badge {{ ($settings['payment_paypal_enabled'] ?? false) ? 'bg-success' : 'bg-secondary' }} me-1">{{ ($settings['payment_paypal_enabled'] ?? false) ? __('Active') : __('Inactive') }}</span>
                                            {{ __('Use this URL in your PayPal developer dashboard webhook settings') }}
                                        </small>
                                    </div>
                                </div>
                                
                                <!-- Paymob Settings -->
                                <div class="col-12 mt-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" wire:model.boolean="settings.payment_paymob_enabled" id="paymob_enabled">
                                        <label class="form-check-label fw-bold" for="paymob_enabled">
                                            {{ __('Enable Paymob') }}
                                        </label>
                                    </div>
                                    <div class="alert alert-info mt-2" role="alert">
                                        <small>
                                            <strong>{{ __('Paymob Setup Instructions') }}:</strong><br>
                                            1. {{ __('Login to your Paymob dashboard') }} (accept.paymob.com)<br>
                                            2. {{ __('Go to Developers > API Keys') }}<br>
                                            3. {{ __('Generate a new API Key (not JWT token)') }}<br>
                                            4. {{ __('Copy Integration ID from your integration settings') }}<br>
                                            5. {{ __('Copy HMAC Secret from webhook settings') }}<br>
                                            6. {{ __('Create an iframe and copy its ID') }}<br>
                                            7. {{ __('Make sure to use the correct credentials for your environment (Test/Live)') }}<br><br>
                                            <strong>{{ __('Note') }}:</strong> {{ __('If you are getting authentication errors, make sure you are using a fresh API Key and not an expired JWT token') }}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Paymob API Key') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.paymob_api_key">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Paymob Integration ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.paymob_integration_id">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Paymob Iframe ID') }}</label>
                                        <input type="text" class="form-control" wire:model="settings.paymob_iframe_id">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('Paymob HMAC Secret') }}</label>
                                        <input type="password" class="form-control" wire:model="settings.paymob_hmac_secret">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" wire:model.boolean="settings.paymob_sandbox" id="paymob_sandbox">
                                            <label class="form-check-label" for="paymob_sandbox">
                                                {{ __('Enable Sandbox Mode (Test)') }}
                                            </label>
                                        </div>
                                        <small class="text-muted">{{ __('Uncheck for Live (Production) mode') }}</small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Paymob Webhook URL') }}</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="{{ url('/webhooks/paymob') }}" readonly style="font-family: monospace; direction: ltr; text-align: left;">
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyWebhookUrl('{{ url('/webhooks/paymob') }}', this)">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <span class="badge {{ ($settings['payment_paymob_enabled'] ?? false) ? 'bg-success' : 'bg-secondary' }} me-1">{{ ($settings['payment_paymob_enabled'] ?? false) ? __('Active') : __('Inactive') }}</span>
                                            {{ __('Use this URL in your Paymob dashboard webhook settings') }}
                                        </small>
                                    </div>
                                </div>
                                

                                
                                <!-- General Payment Settings -->
                                <div class="col-12 mt-4 mb-3">
                                    <h6 class="text-info">{{ __('General Payment Settings') }}</h6>
                                    <hr>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>{{ __('Payment Methods Description') }}</label>
                                        <textarea class="form-control" rows="3" wire:model="settings.payment_methods_description" placeholder="{{ __('Describe accepted payment methods') }}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <script>
                        function copyWebhookUrl(text, button) {
                            navigator.clipboard.writeText(text).then(function() {
                                const originalIcon = button.innerHTML;
                                button.innerHTML = '<i class="fas fa-check"></i>';
                                button.classList.remove('btn-outline-secondary');
                                button.classList.add('btn-success');
                                
                                setTimeout(() => {
                                    button.innerHTML = originalIcon;
                                    button.classList.remove('btn-success');
                                    button.classList.add('btn-outline-secondary');
                                }, 1500);
                            }).catch(function() {
                                alert('{{ __('Failed to copy. Please copy manually.') }}');
                            });
                        }
                        </script>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <small class="text-muted">
                                            {{ __('Last updated') }}: {{ now()->format('Y-m-d H:i:s') }}
                                        </small>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg px-4">
                                            <i class="fas fa-save me-2"></i>
                                            {{ __('Save Settings') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
