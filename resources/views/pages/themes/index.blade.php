<div>
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h3 class="page-title">{{ __('إدارة الثيمات') }}</h3>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
                    <li class="breadcrumb-item active">{{ __('الثيمات') }}</li>
                </ul>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button wire:click="selectedItem('create')" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {{ __('إضافة ثيم جديد') }}
                    </button>
                    <button wire:click="selectedItem('upload')" class="btn btn-success">
                        <i class="fas fa-upload"></i> {{ __('رفع ثيم') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">{{ __('قائمة الثيمات') }}</h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group btn-group-sm" role="group">
                                <button wire:click="toggleTrashed" class="btn {{ $trashed ? 'btn-warning' : 'btn-outline-secondary' }}">
                                    <i class="fas fa-trash"></i>
                                    {{ $trashed ? __('إظهار النشط') : __('إظهار المحذوف') }}
                                </button>
                                <button wire:click="resetFilters" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh"></i> {{ __('إعادة تعيين') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">{{ __('البحث') }}</label>
                            <input type="text" wire:model.live="term" class="form-control" placeholder="{{ __('البحث في الثيمات...') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('النوع') }}</label>
                            <select wire:model.live="selectedType" class="form-select">
                                @foreach($themeTypes as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('الحالة') }}</label>
                            <select wire:model.live="selectedStatus" class="form-select">
                                @foreach($statusOptions as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __('عدد العناصر') }}</label>
                            <select wire:model.live="perPage" class="form-select">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ __('خيارات إضافية') }}</label>
                            <div class="form-check">
                                <input type="checkbox" wire:model.live="showOnlyDefault" class="form-check-input" id="showOnlyDefault">
                                <label class="form-check-label" for="showOnlyDefault">
                                    {{ __('الثيمات الافتراضية فقط') }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Themes Grid -->
                    @if($themes && $themes->count() > 0)
                        <div class="row">
                            @foreach($themes as $theme)
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card theme-card h-100">
                                        <!-- Theme Preview -->
                                        <div class="theme-preview position-relative">
                                            <img src="{{ $theme->getPreviewImageUrl() }}" 
                                                 class="card-img-top" 
                                                 alt="{{ $theme->name }}"
                                                 style="height: 200px; object-fit: cover;">
                                            
                                            <!-- Status Badges -->
                                            <div class="position-absolute top-0 start-0 p-2">
                                                @if($theme->is_default)
                                                    <span class="badge bg-warning">{{ __('افتراضي') }}</span>
                                                @endif
                                                @if(!$theme->is_active)
                                                    <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                                @endif
                                            </div>

                                            <!-- Quick Actions -->
                                            <div class="position-absolute top-0 end-0 p-2">
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button wire:click="selectedItem('preview', {{ $theme->id }})" 
                                                            class="btn btn-light btn-sm" 
                                                            title="{{ __('معاينة') }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button wire:click="selectedItem('customize', {{ $theme->id }})" 
                                                            class="btn btn-light btn-sm" 
                                                            title="{{ __('تخصيص') }}">
                                                        <i class="fas fa-palette"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Theme Info -->
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $theme->name }}</h5>
                                            <p class="card-text text-muted small">{{ Str::limit($theme->description, 100) }}</p>
                                            
                                            <div class="row text-center small text-muted mb-3">
                                                <div class="col-4">
                                                    <div><strong>{{ __('النوع') }}</strong></div>
                                                    <div>{{ $themeTypes[$theme->type] ?? $theme->type }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <div><strong>{{ __('الإصدار') }}</strong></div>
                                                    <div>{{ $theme->version }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <div><strong>{{ __('المؤلف') }}</strong></div>
                                                    <div>{{ Str::limit($theme->author, 15) }}</div>
                                                </div>
                                            </div>

                                            <!-- Usage Stats -->
                                            <div class="text-center mb-3">
                                                <small class="text-muted">
                                                    {{ __('مستخدم في') }} {{ $theme->profiles_count ?? 0 }} {{ __('بروفايل') }}
                                                </small>
                                            </div>
                                        </div>

                                        <!-- Actions -->
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                @if(!$theme->is_default)
                                                    <button wire:click="selectedItem('setDefault', {{ $theme->id }})" 
                                                            class="btn btn-outline-warning btn-sm">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                @endif
                                                
                                                <button wire:click="selectedItem('edit', {{ $theme->id }})" 
                                                        class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <button wire:click="selectedItem('toggleStatus', {{ $theme->id }})" 
                                                        class="btn btn-outline-{{ $theme->is_active ? 'danger' : 'success' }} btn-sm">
                                                    <i class="fas fa-{{ $theme->is_active ? 'pause' : 'play' }}"></i>
                                                </button>
                                                
                                                @if(!$theme->is_default)
                                                    <button wire:click="selectedItem('delete', {{ $theme->id }})" 
                                                            class="btn btn-outline-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $themes->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-palette fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __('لا توجد ثيمات') }}</h5>
                            <p class="text-muted">{{ __('لم يتم العثور على أي ثيمات. قم بإضافة ثيم جديد أو رفع ثيم موجود.') }}</p>
                            <button wire:click="selectedItem('create')" class="btn btn-primary me-2">
                                <i class="fas fa-plus"></i> {{ __('إضافة ثيم جديد') }}
                            </button>
                            <button wire:click="selectedItem('upload')" class="btn btn-success">
                                <i class="fas fa-upload"></i> {{ __('رفع ثيم') }}
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('تأكيد الحذف') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('هل أنت متأكد من حذف هذا الثيم؟ هذا الإجراء لا يمكن التراجع عنه.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('إلغاء') }}</button>
                    <button type="button" wire:click="deleteTheme" class="btn btn-danger">{{ __('حذف') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Set Default Modal -->
    <div class="modal fade" id="setDefaultModal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('تعيين كافتراضي') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('هل تريد تعيين هذا الثيم كافتراضي؟ سيتم إلغاء تعيين الثيم الافتراضي الحالي.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('إلغاء') }}</button>
                    <button type="button" wire:click="setAsDefault" class="btn btn-warning">{{ __('تعيين كافتراضي') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('show-delete-modal', () => {
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });

        Livewire.on('show-set-default-modal', () => {
            new bootstrap.Modal(document.getElementById('setDefaultModal')).show();
        });
    });
</script>
@endpush

@push('styles')
<style>
.theme-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e3e6f0;
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.theme-preview {
    overflow: hidden;
}

.theme-preview img {
    transition: transform 0.3s ease;
}

.theme-card:hover .theme-preview img {
    transform: scale(1.05);
}

.btn-group-vertical .btn {
    border-radius: 0.25rem !important;
    margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
</style>
@endpush
