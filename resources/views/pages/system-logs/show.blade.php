<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3>{{ __('Log Details') }}</h3>
                        <a href="{{ route('system-logs.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {{ __('Back to Logs') }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Basic Info -->
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('Basic Information') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>{{ __('ID') }}:</strong></td>
                                            <td>{{ $log->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Date') }}:</strong></td>
                                            <td>{{ $log->logged_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Level') }}:</strong></td>
                                            <td>
                                                @if($log->level == 'error' || $log->level == 'critical')
                                                    <span class="badge bg-danger">{{ $log->level }}</span>
                                                @elseif($log->level == 'warning')
                                                    <span class="badge bg-warning">{{ $log->level }}</span>
                                                @elseif($log->level == 'info')
                                                    <span class="badge bg-info">{{ $log->level }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $log->level }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Action') }}:</strong></td>
                                            <td><code>{{ $log->action }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Status') }}:</strong></td>
                                            <td>
                                                @if($log->status == 'success')
                                                    <span class="badge bg-success">{{ $log->status }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ $log->status }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Module') }}:</strong></td>
                                            <td>
                                                @if($log->module)
                                                    <span class="badge bg-light text-dark">{{ $log->module }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- User & Request Info -->
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('User & Request Info') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>{{ __('User') }}:</strong></td>
                                            <td>
                                                @if($log->user)
                                                    <i class="fas fa-user-circle"></i> {{ $log->user->name }}
                                                @else
                                                    <span class="text-muted">{{ __('System') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('IP Address') }}:</strong></td>
                                            <td>{{ $log->ip_address ?? __('N/A') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Method') }}:</strong></td>
                                            <td>
                                                @if($log->method)
                                                    <span class="badge bg-primary">{{ $log->method }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('URL') }}:</strong></td>
                                            <td class="small">
                                                @if($log->url)
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-2">{{ $log->url }}</span>
                                                        <a href="{{ $log->url }}" target="_blank" class="btn btn-outline-primary btn-sm" title="{{ __('Visit Page') }}">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </a>
                                                    </div>
                                                @else
                                                    {{ __('N/A') }}
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Response Time') }}:</strong></td>
                                            <td>
                                                @if($log->response_time)
                                                    {{ $log->response_time }}ms
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Session ID') }}:</strong></td>
                                            <td class="small">{{ Str::limit($log->session_id, 20) ?? __('N/A') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('Description') }}</h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0">{{ $log->description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Model Info -->
                    @if($log->model || $log->old_values || $log->new_values)
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('Model Information') }}</h5>
                                </div>
                                <div class="card-body">
                                    @if($log->model)
                                        <p><strong>{{ __('Model') }}:</strong> <code>{{ $log->model }}</code></p>
                                        <p><strong>{{ __('Model ID') }}:</strong> {{ $log->model_id }}</p>
                                    @endif
                                    
                                    @if($log->old_values)
                                        <div class="mt-3">
                                            <h6>{{ __('Old Values') }}:</h6>
                                            <pre class="bg-white p-2 border rounded small">{{ json_encode($log->old_values, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                        </div>
                                    @endif
                                    
                                    @if($log->new_values)
                                        <div class="mt-3">
                                            <h6>{{ __('New Values') }}:</h6>
                                            <pre class="bg-white p-2 border rounded small">{{ json_encode($log->new_values, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Request/Response Data -->
                    @if($log->request_data || $log->response_data || $log->metadata)
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('Additional Data') }}</h5>
                                </div>
                                <div class="card-body">
                                    @if($log->request_data)
                                        <div class="mb-3">
                                            <h6>{{ __('Request Data') }}:</h6>
                                            <pre class="bg-white p-2 border rounded small">{{ json_encode($log->request_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                        </div>
                                    @endif
                                    
                                    @if($log->response_data)
                                        <div class="mb-3">
                                            <h6>{{ __('Response Data') }}:</h6>
                                            <pre class="bg-white p-2 border rounded small">{{ json_encode($log->response_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                        </div>
                                    @endif
                                    
                                    @if($log->metadata)
                                        <div class="mb-3">
                                            <h6>{{ __('Metadata') }}:</h6>
                                            <pre class="bg-white p-2 border rounded small">{{ json_encode($log->metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- User Agent -->
                    @if($log->user_agent)
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent">
                                    <h5 class="mb-0">{{ __('User Agent') }}</h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0 small">{{ $log->user_agent }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>