<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header p-3">
                    <div class="d-flex justify-content-between">
                        <h3>{{ __('System Logs') }}</h3>
                        <button wire:click="clearFilters" class="btn btn-secondary">
                            <i class="fas fa-filter"></i> {{ __('Clear Filters') }}
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-2">
                            <select wire:model.live="level" class="form-select form-select-sm">
                                <option value="">{{ __('All Levels') }}</option>
                                <option value="debug">Debug</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select wire:model.live="user_id" class="form-select form-select-sm">
                                <option value="">{{ __('All Users') }}</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input wire:model.live.debounce.300ms="action" type="text"
                                class="form-control form-control-sm" placeholder="{{ __('Action') }}">
                        </div>
                        <div class="col-md-2">
                            <input wire:model.live.debounce.300ms="module" type="text"
                                class="form-control form-control-sm" placeholder="{{ __('Module') }}">
                        </div>
                        <div class="col-md-4">
                            <input wire:model.live.debounce.500ms="search" type="text"
                                class="form-control form-control-sm" placeholder="{{ __('Search') }}">
                        </div>
                    </div>

                    <div wire:loading class="text-center mb-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        {{ __('Loading...') }}
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center">{{ __('Date') }}</th>
                                    <th class="text-center">{{ __('Level') }}</th>
                                    <th class="text-center">{{ __('Action') }}</th>
                                    <th class="text-center">{{ __('User') }}</th>
                                    <th class="text-center">{{ __('Description') }}</th>
                                    <th class="text-center">{{ __('Module') }}</th>
                                    <th class="text-center">{{ __('IP') }}</th>
                                    <th class="text-center">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($logs as $log)
                                    <tr>
                                        <td class="text-center small">
                                            <div>{{ $log->logged_at->format('Y-m-d') }}</div>
                                            <div class="text-muted">{{ $log->logged_at->format('H:i:s') }}</div>
                                        </td>
                                        <td class="text-center">
                                            @if ($log->level == 'error' || $log->level == 'critical')
                                                <span class="badge bg-danger">{{ $log->level }}</span>
                                            @elseif($log->level == 'warning')
                                                <span class="badge bg-warning">{{ $log->level }}</span>
                                            @elseif($log->level == 'info')
                                                <span class="badge bg-info">{{ $log->level }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $log->level }}</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <code class="small">{{ $log->action }}</code>
                                        </td>
                                        <td class="text-center">
                                            @if ($log->user)
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-user-circle me-1 mr-2 ml-2"></i>
                                                    {{ ' '.$log->user->name.' ' }}
                                                </div>
                                            @else
                                                <span class="text-muted">{{ __('System') }}</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <span class="me-2" title="{{ $log->description }}">{{ Str::limit($log->description, 40) }}</span>
                                                @if($log->url)
                                                    <a href="{{ $log->url }}" target="_blank"
                                                        class="btn btn-outline-primary btn-sm"
                                                        title="{{ __('Visit Page') }}">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if ($log->module)
                                                <span class="badge bg-light text-dark">{{ $log->module }}</span>
                                            @endif
                                        </td>
                                        <td class="text-center small text-muted">
                                            <div>
                                                {{ $log->ip_address }}
                                                @if($log->ip_address && $log->ip_address !== '127.0.0.1' && $log->ip_address !== 'localhost')
                                                    @php
                                                        $ipInfo = @json_decode(file_get_contents("http://ip-api.com/json/{$log->ip_address}"), true);
                                                    @endphp
                                                    @if($ipInfo && $ipInfo['status'] === 'success')
                                                        <div class="text-info">{{ $ipInfo['country'] ?? '' }}{{ isset($ipInfo['city']) ? ', ' . $ipInfo['city'] : '' }}</div>
                                                    @endif
                                                @endif
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <a href="{{ route('system-logs.show', $log->id) }}"
                                                class="btn btn-outline-primary btn-sm"
                                                title="{{ __('View Details') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-center" colspan="8">
                                            <div class="py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <div>{{ __('No logs found') }}</div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if ($logs->hasPages())
                        {{ $logs->links() }}
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
