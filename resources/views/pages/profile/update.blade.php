<div class="card">
    <div class="card-body">
        @if (session()->has('message'))
            <div class="alert alert-success" role="alert">
                {{ session('message') }}
            </div>
        @endif

        <form wire:submit.prevent="save">
            <!-- Language Tabs -->
            @if (count($availablelanguages) > 1)
                <ul class="nav nav-tabs" id="languageTab" role="tablist" wire:ignore>
                    @foreach ($availablelanguages as $language)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {{ $loop->first ? 'active' : '' }}" id="{{ $language->short }}-tab" 
                                    data-bs-toggle="tab" data-bs-target="#{{ $language->short }}" type="button" 
                                    role="tab" aria-controls="{{ $language->short }}" 
                                    aria-selected="{{ $loop->first ? 'true' : 'false' }}">
                                {{ $language->name }}
                            </button>
                        </li>
                    @endforeach
                </ul>
            @endif

            <div class="tab-content" id="languageTabContent" wire:ignore>
                @foreach ($availablelanguages as $language)
                    <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}" id="{{ $language->short }}" 
                         role="tabpanel" aria-labelledby="{{ $language->short }}-tab">
                        
                        <div class="row mb-4 mt-4">
                            <div class="col-6">
                                <label for="name_{{ $language->short }}" class="form-label">
                                    {{ __('Profile Name') }} - {{ $language->name }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" wire:model.defer="name.{{ $language->short }}" 
                                       class="form-control" id="name_{{ $language->short }}">
                                <div class="mt-2 text-danger">
                                    @error('name.' . $language->short)
                                        {{ $message }}
                                    @enderror
                                </div>
                            </div>
                            <div class="col-6">
                                <label for="job_title_{{ $language->short }}" class="form-label">
                                    {{ __('Job Title') }} - {{ $language->name }}
                                </label>
                                <input type="text" wire:model.defer="job_title.{{ $language->short }}" 
                                       class="form-control" id="job_title_{{ $language->short }}">
                                <div class="mt-2 text-danger">
                                    @error('job_title.' . $language->short)
                                        {{ $message }}
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="bio_{{ $language->short }}" class="form-label">
                                    {{ __('Bio') }} - {{ $language->name }}
                                </label>
                                <textarea wire:model.defer="bio.{{ $language->short }}" 
                                          class="form-control" id="bio_{{ $language->short }}" rows="4"></textarea>
                                <div class="mt-2 text-danger">
                                    @error('bio.' . $language->short)
                                        {{ $message }}
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="address_{{ $language->short }}" class="form-label">
                                    {{ __('Address') }} - {{ $language->name }}
                                </label>
                                <textarea wire:model.defer="address.{{ $language->short }}" 
                                          class="form-control" id="address_{{ $language->short }}" rows="2"></textarea>
                                <div class="mt-2 text-danger">
                                    @error('address.' . $language->short)
                                        {{ $message }}
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Non-translatable fields -->
            <div class="row mb-4">
                <div class="col-6">
                    <label for="slug" class="form-label">{{ __('Profile Slug') }} <span class="text-danger">*</span></label>
                    <input type="text" wire:model.live="slug" class="form-control" id="slug">
                    <div class="mt-2 text-danger">
                        @error('slug')
                            {{ $message }}
                        @enderror
                    </div>
                    <small class="text-muted">{{ __('URL will be') }}: {{ url('/professional') }}/{{ $slug ?: 'your-slug' }}</small>
                </div>
                <div class="col-6">
                    <label for="email" class="form-label">{{ __('Email') }}</label>
                    <input type="email" wire:model.live="email" class="form-control" id="email">
                    <div class="mt-2 text-danger">
                        @error('email')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="phone" class="form-label">{{ __('Phone') }}</label>
                    <input type="text" wire:model.live="phone" class="form-control" id="phone">
                    <div class="mt-2 text-danger">
                        @error('phone')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <label for="website" class="form-label">{{ __('Website') }}</label>
                    <input type="url" wire:model.live="website" class="form-control" id="website">
                    <div class="mt-2 text-danger">
                        @error('website')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <label for="domain" class="form-label">{{ __('Custom Domain') }}</label>
                    <input type="text" wire:model.live="domain" class="form-control" id="domain" placeholder="example.com">
                    <div class="mt-2 text-danger">
                        @error('domain')
                            {{ $message }}
                        @enderror
                    </div>
                    <small class="text-muted">{{ __('Enter your custom domain without http:// or https://') }}</small>
                </div>
            </div>



            <div class="row mb-4">
                <div class="col-6">
                    <label for="avatar" class="form-label">{{ __('Avatar') }}</label>
                    <input type="file" wire:model="avatar" class="form-control" id="avatar" accept="image/*">
                    <div class="mt-2 text-danger">
                        @error('avatar')
                            {{ $message }}
                        @enderror
                    </div>
                    @if ($avatar)
                        <div class="mt-2">
                            <img src="{{ $avatar->temporaryUrl() }}" alt="Preview" class="img-thumbnail" width="100">
                        </div>
                    @elseif($profile && $profile->getFirstMediaUrl('avatar'))
                        <div class="mt-2">
                            <img src="{{ $profile->getFirstMediaUrl('avatar') }}" alt="Current Avatar" class="img-thumbnail" width="100">
                            <p class="small text-muted">{{ __('Current Avatar') }}</p>
                        </div>
                    @endif
                </div>
                <div class="col-6">
                    <label for="cover" class="form-label">{{ __('Cover Image') }}</label>
                    <input type="file" wire:model="cover" class="form-control" id="cover" accept="image/*">
                    <div class="mt-2 text-danger">
                        @error('cover')
                            {{ $message }}
                        @enderror
                    </div>
                    @if ($cover)
                        <div class="mt-2">
                            <img src="{{ $cover->temporaryUrl() }}" alt="Preview" class="img-thumbnail" width="150">
                        </div>
                    @elseif($profile && $profile->getFirstMediaUrl('cover'))
                        <div class="mt-2">
                            <img src="{{ $profile->getFirstMediaUrl('cover') }}" alt="Current Cover" class="img-thumbnail" width="150">
                            <p class="small text-muted">{{ __('Current Cover') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="visibility" class="form-label">{{ __('Visibility') }}</label>
                    <select wire:model.live="visibility" class="form-control" id="visibility">
                        <option value="public">{{ __('Public') }}</option>
                        <option value="private">{{ __('Private') }}</option>
                    </select>
                    <div class="mt-2 text-danger">
                        @error('visibility')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" wire:model.live="is_active" id="is_active">
                        <label class="form-check-label" for="is_active">
                            {{ __('Active Profile') }}
                        </label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
                        <span wire:loading.remove>{{ __('Update Profile') }}</span>
                        <span wire:loading>{{ __('Updating...') }}</span>
                    </button>
                    <button type="button" wire:click="cancel" class="btn btn-secondary ms-2">
                        {{ __('Cancel') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
