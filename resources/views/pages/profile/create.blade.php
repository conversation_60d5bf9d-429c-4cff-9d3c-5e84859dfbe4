<div class="card">
    <div class="card-body">
        @if (session()->has('message'))
            <div class="alert alert-success" role="alert">
                {{ session('message') }}
            </div>
        @endif

        <form wire:submit.prevent="create">
            <div class="row mb-4">
                <div class="col-6">
                    <label for="name" class="form-label">{{ __('Profile Name') }} <span class="text-danger">*</span></label>
                    <input type="text" wire:model.live="name" class="form-control" id="name">
                    <div class="mt-2 text-danger">
                        @error('name')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <label for="slug" class="form-label">{{ __('Profile Slug') }} <span class="text-danger">*</span></label>
                    <input type="text" wire:model.live="slug" class="form-control" id="slug">
                    <div class="mt-2 text-danger">
                        @error('slug')
                            {{ $message }}
                        @enderror
                    </div>
                    <small class="text-muted">{{ __('URL will be') }}: {{ url('/professional') }}/{{ $slug ?: 'your-slug' }}</small>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="job_title" class="form-label">{{ __('Job Title') }}</label>
                    <input type="text" wire:model.live="job_title" class="form-control" id="job_title">
                    <div class="mt-2 text-danger">
                        @error('job_title')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <label for="email" class="form-label">{{ __('Email') }}</label>
                    <input type="email" wire:model.live="email" class="form-control" id="email">
                    <div class="mt-2 text-danger">
                        @error('email')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="phone" class="form-label">{{ __('Phone') }}</label>
                    <input type="text" wire:model.live="phone" class="form-control" id="phone">
                    <div class="mt-2 text-danger">
                        @error('phone')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <label for="website" class="form-label">{{ __('Website') }}</label>
                    <input type="url" wire:model.live="website" class="form-control" id="website">
                    <div class="mt-2 text-danger">
                        @error('website')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <label for="domain" class="form-label">{{ __('Custom Domain') }}</label>
                    <input type="text" wire:model.live="domain" class="form-control" id="domain" placeholder="example.com">
                    <div class="mt-2 text-danger">
                        @error('domain')
                            {{ $message }}
                        @enderror
                    </div>
                    <small class="text-muted">{{ __('Enter your custom domain without http:// or https://') }}</small>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <label for="bio" class="form-label">{{ __('Bio') }}</label>
                    <textarea wire:model.live="bio" class="form-control" id="bio" rows="4"></textarea>
                    <div class="mt-2 text-danger">
                        @error('bio')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <label for="address" class="form-label">{{ __('Address') }}</label>
                    <textarea wire:model.live="address" class="form-control" id="address" rows="2"></textarea>
                    <div class="mt-2 text-danger">
                        @error('address')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="avatar" class="form-label">{{ __('Avatar') }}</label>
                    <input type="file" wire:model="avatar" class="form-control" id="avatar" accept="image/*">
                    <div class="mt-2 text-danger">
                        @error('avatar')
                            {{ $message }}
                        @enderror
                    </div>
                    @if ($avatar)
                        <div class="mt-2">
                            <img src="{{ $avatar->temporaryUrl() }}" alt="Preview" class="img-thumbnail" width="100">
                        </div>
                    @endif
                </div>
                <div class="col-6">
                    <label for="cover" class="form-label">{{ __('Cover Image') }}</label>
                    <input type="file" wire:model="cover" class="form-control" id="cover" accept="image/*">
                    <div class="mt-2 text-danger">
                        @error('cover')
                            {{ $message }}
                        @enderror
                    </div>
                    @if ($cover)
                        <div class="mt-2">
                            <img src="{{ $cover->temporaryUrl() }}" alt="Preview" class="img-thumbnail" width="150">
                        </div>
                    @endif
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <label for="visibility" class="form-label">{{ __('Visibility') }}</label>
                    <select wire:model.live="visibility" class="form-control" id="visibility">
                        <option value="public">{{ __('Public') }}</option>
                        <option value="private">{{ __('Private') }}</option>
                    </select>
                    <div class="mt-2 text-danger">
                        @error('visibility')
                            {{ $message }}
                        @enderror
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" wire:model.live="is_active" id="is_active">
                        <label class="form-check-label" for="is_active">
                            {{ __('Active Profile') }}
                        </label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
                        <span wire:loading.remove>{{ __('Create Profile') }}</span>
                        <span wire:loading>{{ __('Creating...') }}</span>
                    </button>
                    <button type="button" wire:click="cancel" class="btn btn-secondary ms-2">
                        {{ __('Cancel') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
