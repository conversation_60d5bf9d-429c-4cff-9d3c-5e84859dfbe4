<div>
    <div class="container-fluid">
        <div class="row">
            <div class="card">
                <div class="card-body">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-end text-center">
                                    <div class="col col-2">
                                        <label class="form-check-label" for="search">{{ __('search') }}</label>
                                        <input wire:model.live="term" id="search" type="text"
                                            class="form-control mt-1" autocomplete="off" />
                                    </div>
                                    <div class="col col-2">
                                        <label class="form-check-label" for="select">{{ __('OrderBy') }}</label>
                                        <select wire:model.live="orderBy" class="form-control mt-1">
                                            <option value="id">{{ __('id') }}</option>
                                            <option value="name">{{ __('Name') }}</option>
                                            <option value="slug">{{ __('Slug') }}</option>
                                            @if ($trashed)
                                                <option value="deleted_at">{{ __('deleted_at') }}</option>
                                            @else
                                                <option value="created_at">{{ __('created_at') }}</option>
                                                <option value="updated_at">{{ __('updated_at') }}</option>
                                            @endif
                                        </select>
                                    </div>

                                    <div class="col col-2">
                                        <label class="form-check-label" for="select">{{ __('PerPage') }}</label>
                                        <select wire:model.live="perPage" class="form-control mt-1">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                        </select>
                                    </div>

                                    <div class="col col-2">
                                        <label class="form-check-label" for="select">{{ __('SortBy') }}</label>
                                        <select wire:model.live="sortBy" class="form-control mt-1">
                                            <option value="asc">{{ __('ASC') }}</option>
                                            <option value="desc">{{ __('DESC') }}</option>
                                        </select>
                                    </div>

                                    <div class="form-check form-switch form-switch-lg">
                                        <label class="form-check-label" for="trashed">{{ __('Show Trashed') }}</label>
                                        <input type="checkbox" wire:model.live="trashed" value="true"
                                            id="customSwitchsizelg" class="form-check-input mt-1" />
                                    </div>
                                    <div class="col col-2">
                                        <button type="button" class="btn btn-primary"
                                            wire:click="selectedItem('create',null)"
                                            wire:loading.attr="disabled">{{ __('Create New') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">{{ __('ID') }}</th>
                                    <th class="text-center">{{ __('Avatar') }}</th>
                                    <th class="text-center">{{ __('Name') }}</th>
                                    <th class="text-center">{{ __('Slug') }}</th>
                                    <th class="text-center">{{ __('Job Title') }}</th>
                                    <th class="text-center">{{ __('Status') }}</th>
                                    <th class="text-center">{{ __('Visibility') }}</th>
                                    <th class="text-center">إدارة البروفايل</th>
                                    <th class="text-center">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($profiles as $profile)
                                    <tr>
                                        <td class="text-center">{{ $profile->id }}</td>
                                        <td class="text-center">
                                            @if($profile->getFirstMediaUrl('avatar'))
                                                <img src="{{ $profile->getFirstMediaUrl('avatar') }}" 
                                                     alt="{{ $profile->name }}" 
                                                     class="rounded-circle" 
                                                     width="40" height="40">
                                            @else
                                                <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td class="text-center">{{ $profile->name }}</td>
                                        <td class="text-center">
                                            <code>{{ $profile->slug }}</code>
                                        </td>
                                        <td class="text-center">{{ $profile->job_title }}</td>
                                        <td class="text-center">
                                            <span class="badge {{ $profile->is_active ? 'bg-success' : 'bg-danger' }}">
                                                {{ $profile->is_active ? __('Active') : __('Inactive') }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge {{ $profile->visibility === 'public' ? 'bg-primary' : 'bg-warning' }}">
                                                {{ __($profile->visibility) }}
                                            </span>
                                        </td>
                                        <td class="text-center" width="300">
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('skills.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-info btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Skills') }}">
                                                    <i class="fas fa-cogs"></i>
                                                </a>
                                                <a href="{{ route('education.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-success btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Education') }}">
                                                    <i class="fas fa-graduation-cap"></i>
                                                </a>
                                                <a href="{{ route('experience.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-warning btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Experience') }}">
                                                    <i class="fas fa-briefcase"></i>
                                                </a>
                                                <a href="{{ route('categories.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-secondary btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Categories') }}">
                                                    <i class="fas fa-tags"></i>
                                                </a>
                                                <a href="{{ route('portfolio.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-primary btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Portfolio') }}">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                                <a href="{{ route('socials.index', ['profile_id' => $profile->id]) }}" 
                                                   class="btn btn-outline-dark btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   data-bs-placement="top" 
                                                   title="{{ __('Social Links') }}">
                                                    <i class="fas fa-share-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                        <td class="text-center" width="250">
                                            <div class="d-flex items-center justify-content-between">
                                                @if ($trashed)
                                                    @can('restore', $profile)
                                                        <button wire:click="selectedItem('restore',{{ $profile->id }})"
                                                            title="{{ __('restore') }}" class="btn btn-outline-warning">
                                                            <i class="fas fa-share"></i>
                                                        </button>
                                                    @endcan
                                                    @can('forceDelete', $profile)
                                                        <button
                                                            wire:click="selectedItem('forceDelete',{{ $profile->id }})"
                                                            title="{{ __('force delete') }}"
                                                            class="btn btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endcan
                                                @else
                                                    @if($profile->visibility === 'public' && $profile->is_active)
                                                        <a href="{{ route('professional.show', $profile->slug) }}" 
                                                           target="_blank" 
                                                           title="{{ __('View') }}" 
                                                           class="btn btn-outline-success">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    @endif
                                                    @can('update', $profile)
                                                        <button wire:click="selectedItem('update',{{ $profile->id }})"
                                                            title="{{ __('Edit') }}" class="btn btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    @endcan
                                                    @can('delete', $profile)
                                                        <button wire:click="selectedItem('delete',{{ $profile->id }})"
                                                            class="btn btn-outline-danger">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    @endcan
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-center" colspan="9">{{ __('No Data Found') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        @if (!empty($profiles))
                            <div class="dataTables_paginate paging_simple_numbers">
                                {{ $profiles->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <livewire:profile.delete />
    
    <style>
    /* إصلاح الحواف الدائرية للأزرار في الاتجاه RTL */
    [dir="rtl"] .btn-group .btn:first-child {
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    
    [dir="rtl"] .btn-group .btn:last-child {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }
    
    [dir="rtl"] .btn-group .btn:not(:first-child):not(:last-child) {
        border-radius: 0 !important;
    }
    
    /* للتأكد من عمل الأزرار في الاتجاه LTR بشكل طبيعي */
    [dir="ltr"] .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }
    
    [dir="ltr"] .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    
    [dir="ltr"] .btn-group .btn:not(:first-child):not(:last-child) {
        border-radius: 0 !important;
    }
    
    /* تخصيص تصميم التول تب */
    .tooltip {
        font-size: 0.75rem;
        font-family: inherit;
    }
    
    .tooltip .tooltip-inner {
        background-color: #2c3e50;
        color: #ffffff;
        border-radius: 6px;
        padding: 6px 10px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .tooltip .tooltip-arrow {
        border-top-color: #2c3e50;
    }
    
    .tooltip.bs-tooltip-bottom .tooltip-arrow {
        border-bottom-color: #2c3e50;
    }
    
    .tooltip.bs-tooltip-start .tooltip-arrow {
        border-left-color: #2c3e50;
    }
    
    .tooltip.bs-tooltip-end .tooltip-arrow {
        border-right-color: #2c3e50;
    }
    </style>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                trigger: 'hover',
                delay: { show: 100, hide: 300 },
                placement: 'top'
            });
        });
    });
    </script>
</div>
