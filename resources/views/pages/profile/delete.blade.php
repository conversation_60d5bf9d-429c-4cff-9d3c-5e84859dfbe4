<div>
    <!-- Delete Modal -->
    @if($showDeleteModel)
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Delete Profile') }}</h5>
                    <button type="button" class="btn-close" wire:click="closeDeleteModel"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('Are you sure you want to delete this profile? This action can be undone later.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeDeleteModel">
                        {{ __('Cancel') }}
                    </button>
                    <button type="button" wire:click="delete" class="btn btn-danger" wire:loading.attr="disabled">
                        <span wire:loading.remove>{{ __('Delete') }}</span>
                        <span wire:loading>{{ __('Deleting...') }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Restore Modal -->
    @if($showRestoreModel)
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Restore Profile') }}</h5>
                    <button type="button" class="btn-close" wire:click="closeRestoreModel"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('Are you sure you want to restore this profile?') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeRestoreModel">
                        {{ __('Cancel') }}
                    </button>
                    <button type="button" wire:click="restore" class="btn btn-success" wire:loading.attr="disabled">
                        <span wire:loading.remove>{{ __('Restore') }}</span>
                        <span wire:loading>{{ __('Restoring...') }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Force Delete Modal -->
    @if($showForceDeleteModel)
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Permanently Delete Profile') }}</h5>
                    <button type="button" class="btn-close" wire:click="closeForceDeleteModel"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>{{ __('Warning!') }}</strong>
                        {{ __('This action cannot be undone. The profile and all its data will be permanently deleted.') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeForceDeleteModel">
                        {{ __('Cancel') }}
                    </button>
                    <button type="button" wire:click="forceDelete" class="btn btn-danger" wire:loading.attr="disabled">
                        <span wire:loading.remove>{{ __('Permanently Delete') }}</span>
                        <span wire:loading>{{ __('Deleting...') }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
