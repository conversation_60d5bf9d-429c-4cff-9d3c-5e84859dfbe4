<div>
    <div class="row">
        <div class="col-12">

        <div class="row">
            <div class="col-6">
                @if (Laravel\Fortify\Features::canUpdateProfileInformation())
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ __('Update Password') }}</h5>
                            <form wire:submit.prevent="updateProfileInformation" class="col-5">
                                <!-- Name -->
                                <div class="col-span-6 sm:col-span-4">
                                    <label for="name">{{ __('Name') }}</label>
                                    <input id="name" type="text" class="form-control" wire:model.defer="state.name"
                                        autocomplete="name" />
                                    @error('name')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Email -->
                                <div class="mt-2">
                                    <label for="email">{{ __('Email') }}</label>
                                    <input id="email" type="email" class="form-control" wire:model.defer="state.email"
                                        autocomplete="username" />
                                    @error('email')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror

                                    @if (Laravel\Fortify\Features::enabled(Laravel\Fortify\Features::emailVerification()) &&
                                            !$this->user->hasVerifiedEmail())
                                        <p class="text-sm mt-2">
                                            {{ __('Your email address is unverified.') }}

                                            <button type="button" class="btn btn-warning"
                                                wire:click.prevent="sendEmailVerification">
                                                {{ __('Click here to re-send the verification email.') }}
                                            </button>
                                        </p>

                                        @if ($this->verificationLinkSent)
                                            <p v-show="verificationLinkSent"
                                                class="mt-2 font-medium text-sm text-green-600">
                                                {{ __('A new verification link has been sent to your email address.') }}
                                            </p>
                                        @endif
                                    @endif
                                </div>
                                <hr class="my-4">
                                <button wire:loading.attr="disabled" wire:target="photo" class="btn btn-outline-primary">
                                    {{ __('Save') }}
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            </div>

            <div class="col-6">
                @if (Laravel\Fortify\Features::canUpdateProfileInformation())
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ __('Two Factor Authentication') }}</h5>
                            <form wire:submit.prevent="updateProfileInformation" class="col-5">
                                <!-- Image -->
                                <div class="text-center mb-3">
                                    <img src="{{ $photo ? $photo->temporaryUrl() : auth()->user()->profile_photo_url ?? 'https://via.placeholder.com/150' }}"
                                        alt="Profile Photo" class="img-thumbnail rounded-circle mx-auto d-block"
                                        style="width: 150px; height: 150px; object-fit: cover;">
                                </div>
                                <div class="col-span-6 sm:col-span-4">
                                    <label for="photo">{{ __('Image') }}</label>
                                    <input id="photo" type="file" class="form-control" wire:model="photo"
                                        accept="image/*" />
                                    @error('photo')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror
                                </div>
                                <button wire:loading.attr="disabled" wire:target="photo"
                                    class="btn btn-outline-primary mt-3">
                                    {{ __('Save') }}
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-6">
                @if (Laravel\Fortify\Features::enabled(Laravel\Fortify\Features::updatePasswords()))
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ __('Update Password') }}</h5>
                            <form wire:submit.prevent="updatePassword">
                                <div class="col-span-6 sm:col-span-4">
                                    <label for="current_password">{{ __('Current Password') }}</label>
                                    <input id="current_password" type="password" class="form-control"
                                        wire:model.defer="state.current_password" autocomplete="current-password" />
                                    @error('current_password')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="mt-2">
                                    <label for="password">{{ __('New Password') }}</label>
                                    <input id="password" type="password" class="form-control"
                                        wire:model.defer="state.password" autocomplete="new-password" />
                                    @error('password')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="mt-2">
                                    <label for="password_confirmation">{{ __('Confirm Password') }}</label>
                                    <input id="password_confirmation" type="password" class="form-control"
                                        wire:model.defer="state.password_confirmation" autocomplete="new-password" />
                                    @error('password_confirmation')
                                        <span class="text-danger mt-2">{{ $message }}</span>
                                    @enderror
                                </div>

                                <button class="btn btn-outline-primary mt-2">
                                    {{ __('Save') }}
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            </div>

            <div class="col-6">
                @if (Laravel\Fortify\Features::canManageTwoFactorAuthentication())
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ __('Two Factor Authentication') }}</h5>
                            @livewire('profile.two-factor-authentication-form')
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ __('Browser Sessions') }}</h5>
                        @livewire('profile.logout-other-browser-sessions-form')
                    </div>
                </div>
            </div>

            <div class="col-6">
                @if (Laravel\Jetstream\Jetstream::hasAccountDeletionFeatures())
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ __('Delete Account') }}</h5>
                            @livewire('profile.delete-user-form')
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
</div>
