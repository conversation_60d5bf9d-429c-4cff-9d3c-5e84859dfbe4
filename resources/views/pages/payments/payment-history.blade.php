<div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">{{ __('Payment History') }}</h4>
                </div>
            </div>
        </div>

        <!-- إحصائيات المدفوعات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-primary text-primary rounded">
                                        <i class="mdi mdi-credit-card-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">{{ __('Total Payments') }}</p>
                                <h5 class="mb-0">{{ $stats['total_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-success text-success rounded">
                                        <i class="mdi mdi-check-circle-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">{{ __('Completed') }}</p>
                                <h5 class="mb-0">{{ $stats['completed_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-info text-info rounded">
                                        <i class="mdi mdi-currency-usd font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">{{ __('Total Amount') }}</p>
                                <h5 class="mb-0">${{ number_format($stats['total_amount'] ?? 0, 2) }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-warning text-warning rounded">
                                        <i class="mdi mdi-clock-outline font-size-18"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="text-muted mb-1">{{ __('Pending') }}</p>
                                <h5 class="mb-0">{{ $stats['pending_payments'] ?? 0 }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">{{ __('Status') }}</label>
                                <select wire:model="filters.status" class="form-select">
                                    <option value="">{{ __('All') }}</option>
                                    <option value="completed">{{ __('Completed') }}</option>
                                    <option value="pending">{{ __('Pending') }}</option>
                                    <option value="failed">{{ __('Failed') }}</option>
                                    <option value="cancelled">{{ __('Cancelled') }}</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('Gateway') }}</label>
                                <select wire:model="filters.gateway" class="form-select">
                                    <option value="">{{ __('All') }}</option>
                                    <option value="paypal">PayPal</option>
                                    <option value="stripe">Stripe</option>
                                    <option value="paymob">Paymob</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('From Date') }}</label>
                                <input type="date" wire:model="filters.date_from" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{{ __('To Date') }}</label>
                                <input type="date" wire:model="filters.date_to" class="form-control">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button wire:click="applyFilters" class="btn btn-primary">{{ __('Filter') }}</button>
                                <button wire:click="resetFilters" class="btn btn-secondary">{{ __('Reset') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المدفوعات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-centered table-nowrap mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>{{ __('Payment ID') }}</th>
                                        <th>{{ __('Subscription') }}</th>
                                        <th>{{ __('Plan') }}</th>
                                        <th>{{ __('Amount') }}</th>
                                        <th>{{ __('Gateway') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Date') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if($this->payments && $this->payments->count() > 0)
                                        @foreach($this->payments as $payment)
                                            <tr>
                                                <td>
                                                    <span class="text-muted">#{{ substr($payment->payment_id, 0, 10) }}...</span>
                                                </td>
                                                <td>
                                                    @if($payment->subscription_id)
                                                        <span class="badge bg-info">#{{ $payment->subscription_id }}</span>
                                                    @else
                                                        <span class="text-muted">{{ __('Not linked') }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($payment->plan)
                                                        {{ $payment->plan->name }}
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <strong>${{ number_format($payment->amount, 2) }}</strong>
                                                    <small class="text-muted d-block">{{ $payment->currency }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark">{{ $payment->gateway_name }}</span>
                                                </td>
                                                <td>{!! $payment->status_badge !!}</td>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span class="text-muted small">{{ __('Created') }}: {{ $payment->created_at->format('Y-m-d H:i') }}</span>
                                                        @if($payment->paid_at)
                                                            <span class="text-success small fw-bold">{{ __('Paid') }}: {{ $payment->paid_at->format('Y-m-d H:i') }}</span>
                                                        @elseif($payment->status === 'completed')
                                                            <span class="text-warning small">{{ __('Completed but no payment date') }}</span>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>
                                                    <button wire:click="showPaymentDetails('{{ $payment->payment_id }}')" class="btn btn-sm btn-outline-primary">
                                                        {{ __('View Details') }}
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="mdi mdi-credit-card-off font-size-48 text-muted mb-2"></i>
                                                    <p class="text-muted">{{ __('No payments found') }}</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>

                        @if($this->payments && $this->payments->hasPages())
                            <div class="row mt-4">
                                <div class="col-12">
                                    {{ $this->payments->links() }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل الدفع -->
    @if($showDetails && $selectedPayment)
        <div class="modal fade show" style="display: block;" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('Payment Details') }}</h5>
                        <button type="button" class="btn-close" wire:click="closeDetails"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>{{ __('Payment Information') }}</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>{{ __('Payment ID') }}:</strong></td>
                                        <td>{{ $selectedPayment->payment_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Amount') }}:</strong></td>
                                        <td>${{ number_format($selectedPayment->amount, 2) }} {{ $selectedPayment->currency }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Gateway') }}:</strong></td>
                                        <td>{{ $selectedPayment->gateway_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Status') }}:</strong></td>
                                        <td>{!! $selectedPayment->status_badge !!}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Type') }}:</strong></td>
                                        <td>{{ ucfirst($selectedPayment->type) }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>{{ __('Dates') }}</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>{{ __('Created At') }}:</strong></td>
                                        <td>{{ $selectedPayment->created_at->format('Y-m-d H:i:s') }}</td>
                                    </tr>
                                    @if($selectedPayment->paid_at)
                                        <tr>
                                            <td><strong>{{ __('Paid At') }}:</strong></td>
                                            <td>{{ $selectedPayment->paid_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                    @endif
                                </table>
                                
                                @if($selectedPayment->subscription)
                                    <h6>{{ __('Subscription Information') }}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{{ __('Subscription ID') }}:</strong></td>
                                            <td>#{{ $selectedPayment->subscription->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Status') }}:</strong></td>
                                            <td>{{ ucfirst($selectedPayment->subscription->status) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Starts At') }}:</strong></td>
                                            <td>{{ $selectedPayment->subscription->starts_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Ends At') }}:</strong></td>
                                            <td>{{ $selectedPayment->subscription->ends_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                    </table>
                                @endif
                                
                                @if($selectedPayment->plan)
                                    <h6>{{ __('Plan Information') }}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{{ __('Plan Name') }}:</strong></td>
                                            <td>{{ $selectedPayment->plan->name }}</td>
                                        </tr>
                                    </table>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeDetails">{{ __('Close') }}</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    @endif
</div>