<div>
    <!-- Header -->
    <header class="header">
        <!-- logo -->
        <div class="logo">
            <a href="#">
                @if($profile->getFirstMediaUrl('avatar'))
                    <img class="logo-img" src="{{ $profile->getFirstMediaUrl('avatar') }}" alt="" />
                @endif
                <span class="logo-lnk">{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}</span>
            </a>
        </div>

        <!-- menu button -->
        <a href="#" class="menu-btn"><span></span></a>

        <!-- header sidebar -->
        <div class="header-sidebar">
            <!-- top menu -->
            <div class="top-menu">
                <div class="top-menu-nav">
                    <div class="menu-topmenu-container">
                        <ul class="menu">
                            <li class="menu-item current-menu-item">
                                <a href="#section-started">
                                    <span class="animated-button"><span>Home</span></span>
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="#section-about">
                                    <span class="animated-button"><span>About</span></span>
                                </a>
                            </li>
                            @if($profile->experiences->count() > 0 || $profile->education->count() > 0)
                            <li class="menu-item">
                                <a href="#section-experience">
                                    <span class="animated-button"><span>Resume</span></span>
                                </a>
                            </li>
                            @endif
                            @if($profile->portfolios->count() > 0)
                            <li class="menu-item">
                                <a href="#section-portfolio">
                                    <span class="animated-button"><span>Portfolio</span></span>
                                </a>
                            </li>
                            @endif
                            <li class="menu-item">
                                <a href="#section-contacts">
                                    <span class="animated-button"><span>Contact</span></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Wrapper -->
    <div class="wrapper">
        <!-- Background -->
        <div class="background-bg">
            <div class="background-filter gradient">
                <div class="background-img" style="background-image: url({{ $profile->getFirstMediaUrl('cover') ?: asset('assets/v1/images/bg.jpg') }});"></div>
            </div>
        </div>

        <!-- Section Started -->
        <div class="section started" id="section-started">
            <div class="centrize full-width">
                <div class="vertical-center">
                    <!-- title -->
                    <h1 class="h-title">
                        {{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}
                    </h1>

                    <!-- content started -->
                    <div class="started-content">
                        <!-- subtitle -->
                        @if($profile->getTranslation('job_title', app()->getLocale()))
                        <div class="h-subtitles">
                            <div class="h-subtitle typing-subtitle">
                                <p>{{ $profile->getTranslation('job_title', app()->getLocale()) }}</p>
                            </div>
                            <span class="typed-subtitle"></span>
                        </div>
                        @endif

                        <!-- text -->
                        @if($profile->getTranslation('bio', app()->getLocale()))
                        <div class="h-text">
                            {{ $profile->getTranslation('bio', app()->getLocale()) }}
                        </div>
                        @endif

                        <!-- button -->
                        <a href="#section-contacts" class="btn">
                            <span class="animated-button"><span>Contact Me</span></span>
                            <i class="icon fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section About -->
        <div class="section about" id="section-about">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">About Me</div>
                    <div class="subtitle">My story</div>
                </div>

                <!-- text -->
                @if($profile->getTranslation('bio', app()->getLocale()))
                <div class="cols">
                    <div class="col col-full">
                        <div class="single-post-text">
                            <p>{{ $profile->getTranslation('bio', app()->getLocale()) }}</p>
                        </div>
                    </div>
                </div>
                @endif

                <!-- info list -->
                <div class="info-list">
                    <ul>
                        @if($profile->getTranslation('address', app()->getLocale()))
                        <li><strong>Address:</strong> {{ $profile->getTranslation('address', app()->getLocale()) }}</li>
                        @endif
                        @if($profile->phone)
                        <li><strong>Phone:</strong> {{ $profile->phone }}</li>
                        @endif
                        @if($profile->email)
                        <li><strong>E-mail:</strong> {{ $profile->email }}</li>
                        @endif
                        @if($profile->website)
                        <li><strong>Website:</strong> <a href="{{ $profile->website }}" target="_blank">{{ $profile->website }}</a></li>
                        @endif
                    </ul>
                </div>
                <div class="clear"></div>
            </div>
        </div>
        <!-- Section Resume Experience -->
        @if($profile->experiences->count() > 0)
        <div class="section resume" id="section-experience">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">Experience</div>
                    <div class="subtitle">Working with</div>
                </div>

                <!-- resume items -->
                <div class="content-carousel">
                    <div class="owl-carousel" data-slidesview="2" data-slidesview_mobile="1">
                        @foreach($profile->experiences as $experience)
                        <div class="item">
                            <div class="resume-item active">
                                <div class="date">{{ $experience->FromDate }} - {{ $experience->ToDate }}</div>
                                <div class="name">
                                    {{ $experience->specialization }}<br />{{ $experience->entity_name }}
                                </div>
                                @if($experience->Note)
                                <div class="single-post-text">
                                    <p>{{ $experience->Note }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- navigation -->
                    <div class="navs">
                        <span class="prev fas fa-chevron-left"></span>
                        <span class="next fas fa-chevron-right"></span>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Section Resume Education -->
        @if($profile->education->count() > 0)
        <div class="section resume" id="section-education">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">Education</div>
                    <div class="subtitle">Studied at</div>
                </div>

                <!-- resume items -->
                <div class="content-carousel">
                    <div class="owl-carousel" data-slidesview="2" data-slidesview_mobile="1">
                        @foreach($profile->education as $edu)
                        <div class="item">
                            <div class="resume-item active">
                                <div class="date">{{ $edu->FromDate }} - {{ $edu->ToDate }}</div>
                                <div class="name">
                                    {{ $edu->specialization }}<br />{{ $edu->entity_name }}
                                </div>
                                @if($edu->Note)
                                <div class="single-post-text">
                                    <p>{{ $edu->Note }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- navigation -->
                    <div class="navs">
                        <span class="prev fas fa-chevron-left"></span>
                        <span class="next fas fa-chevron-right"></span>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Section Skills -->
        @if($profile->skills->count() > 0)
        <div class="section skills" id="section-skills-code">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">Skills</div>
                    <div class="subtitle">Developing on</div>
                </div>

                <!-- skills items-->
                <div class="skills circles">
                    <ul>
                        @foreach($profile->skills as $skill)
                        <li>
                            <div class="progress p75">
                                <div class="percentage"></div>
                                <span>75%</span>
                            </div>
                            <div class="name">{{ $skill->skill }}</div>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        @endif

        <!-- Works -->
        @if($profile->portfolios->count() > 0)
        <div class="section works" id="section-portfolio">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">Portfolio</div>
                    <div class="subtitle">Latest works</div>
                </div>

                <!-- portfolio items -->
                <div class="box-items">
                    @foreach($profile->portfolios as $portfolio)
                    <div class="box-col">
                        <div class="box-item">
                            <div class="image">
                                <a href="#gallery-{{ $portfolio->id }}" class="has-popup-gallery">
                                    @if($portfolio->getFirstMediaUrl('images'))
                                        <img src="{{ $portfolio->getFirstMediaUrl('images') }}" alt="" />
                                    @endif
                                    <span class="info">
                                        <span class="centrize full-width">
                                            <span class="vertical-center">
                                                <i class="icon fas fa-images"></i>
                                            </span>
                                        </span>
                                    </span>
                                </a>
                                <div id="gallery-{{ $portfolio->id }}" class="mfp-hide">
                                    @foreach($portfolio->getMedia('images') as $imageItem)
                                        <a href="{{ $imageItem->getUrl() }}"></a>
                                    @endforeach
                                </div>
                            </div>
                            <div class="desc">
                                <div class="category">{{ Str::limit($portfolio->content, 50) }}</div>
                                @if($portfolio->Project_URL)
                                    <a href="{{ $portfolio->Project_URL }}" target="_blank" class="name">{{ $portfolio->title }}</a>
                                @else
                                    <span class="name">{{ $portfolio->title }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="clear"></div>
            </div>
        </div>
        @endif

        <!-- Section Contacts -->
        <div class="section contacts" id="section-contacts">
            <div class="content">
                <!-- title -->
                <div class="titles">
                    <div class="title">Contact</div>
                    <div class="subtitle">Let's talk</div>
                </div>

                <!-- contact info -->
                <div class="contact-info">
                    <div class="name">{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}</div>
                    @if($profile->getTranslation('job_title', app()->getLocale()))
                    <div class="subname">{{ $profile->getTranslation('job_title', app()->getLocale()) }}</div>
                    @endif
                    <div class="info-list">
                        <ul>
                            @if($profile->getTranslation('address', app()->getLocale()))
                            <li><strong>Address:</strong> {{ $profile->getTranslation('address', app()->getLocale()) }}</li>
                            @endif
                            @if($profile->phone)
                            <li><strong>Phone:</strong> {{ $profile->phone }}</li>
                            @endif
                            @if($profile->email)
                            <li><strong>E-mail:</strong> {{ $profile->email }}</li>
                            @endif
                            @if($profile->website)
                            <li><strong>Website:</strong> <a href="{{ $profile->website }}" target="_blank">{{ $profile->website }}</a></li>
                            @endif
                        </ul>
                    </div>
                    <div class="author">{{ $profile->getTranslation('name', app()->getLocale()) ?: 'Profile' }}</div>
                </div>
                <div class="clear"></div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    @if($profile->socials->count() > 0)
    <footer class="footer">
        <div class="socials">
            @foreach($profile->socials as $social)
                <a target="_blank" href="{{ $social->url }}">
                    <i class="fab fa-{{ strtolower($social->platform) }}"></i>
                </a>
            @endforeach
        </div>
    </footer>
    @endif
</div>