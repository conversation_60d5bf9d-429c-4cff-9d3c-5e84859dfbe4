<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Payment;
use App\Models\Subscription;
use Carbon\Carbon;

class FixPaymentData extends Command
{
    protected $signature = 'payments:fix-data';
    protected $description = 'إصلاح بيانات المدفوعات وربطها بالاشتراكات';

    public function handle()
    {
        $this->info('بدء إصلاح بيانات المدفوعات...');

        // إصلاح تواريخ المدفوعات المكتملة التي لا تحتوي على paid_at
        $paymentsWithoutPaidAt = Payment::where('status', 'completed')
            ->whereNull('paid_at')
            ->get();

        $this->info("وجد {$paymentsWithoutPaidAt->count()} مدفوعة مكتملة بدون تاريخ دفع");

        foreach ($paymentsWithoutPaidAt as $payment) {
            $payment->update(['paid_at' => $payment->updated_at]);
            $this->line("تم إصلاح تاريخ الدفع للمدفوعة: {$payment->payment_id}");
        }

        // ربط المدفوعات بالاشتراكات
        $paymentsWithoutSubscription = Payment::whereNull('subscription_id')
            ->where('status', 'completed')
            ->get();

        $this->info("وجد {$paymentsWithoutSubscription->count()} مدفوعة بدون ربط بالاشتراك");

        foreach ($paymentsWithoutSubscription as $payment) {
            // البحث عن اشتراك للمستخدم بنفس الخطة تم إنشاؤه في نفس الوقت تقريباً
            $subscription = Subscription::where('user_id', $payment->user_id)
                ->where('plan_id', $payment->plan_id)
                ->whereBetween('created_at', [
                    $payment->created_at->subMinutes(10),
                    $payment->created_at->addMinutes(10)
                ])
                ->first();

            if ($subscription) {
                $payment->update(['subscription_id' => $subscription->id]);
                $this->line("تم ربط المدفوعة {$payment->payment_id} بالاشتراك {$subscription->id}");
            }
        }

        $this->info('تم الانتهاء من إصلاح بيانات المدفوعات');
    }
}