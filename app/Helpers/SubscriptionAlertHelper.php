<?php

namespace App\Helpers;

use App\Models\User;

class SubscriptionAlertHelper
{
    /**
     * Generate subscription limit alert data for Collections
     *
     * @param User $user
     * @return array
     */
    public static function getCollectionLimitAlert(User $user): array
    {
        $currentLimit = $user->getFeatureLimit('create-collection');
        $usedCollections = $user->collections()->count();
        
        $message = __('You have reached the maximum limit for creating Collections in your current plan. Please upgrade your plan to create more Collections.');
        $details = __('Collections used') . ': ' . $usedCollections . ' / ' . ($usedCollections + $currentLimit);
        
        return [
            'type' => 'error',
            'message' => $message,
            'details' => $details,
            'options' => [
                'position' => 'center',
                'timer' => 8000,
                'toast' => false,
                'text' => $details,
                'showConfirmButton' => true,
                'confirmButtonText' => __('Upgrade Plan'),
                'confirmButtonColor' => '#3085d6',
                'showCancelButton' => true,
                'cancelButtonText' => __('Close'),
            ]
        ];
    }

    /**
     * Generate subscription limit alert data for LinkTrees
     *
     * @param User $user
     * @return array
     */
    public static function getLinkTreeLimitAlert(User $user): array
    {
        $currentLimit = $user->getFeatureLimit('create-link-tree');
        $usedLinkTrees = $user->linkTrees()->count();
        
        $message = __('You have reached the maximum limit for creating LinkTrees in your current plan. Please upgrade your plan to create more LinkTrees.');
        $details = __('LinkTrees used') . ': ' . $usedLinkTrees . ' / ' . ($usedLinkTrees + $currentLimit);
        
        return [
            'type' => 'error',
            'message' => $message,
            'details' => $details,
            'options' => [
                'position' => 'center',
                'timer' => 8000,
                'toast' => false,
                'text' => $details,
                'showConfirmButton' => true,
                'confirmButtonText' => __('Upgrade Plan'),
                'confirmButtonColor' => '#3085d6',
                'showCancelButton' => true,
                'cancelButtonText' => __('Close'),
            ]
        ];
    }

    /**
     * Generate generic feature limit alert
     *
     * @param User $user
     * @param string $featureSlug
     * @param string $featureName
     * @param string $usedCountMethod
     * @return array
     */
    public static function getFeatureLimitAlert(User $user, string $featureSlug, string $featureName, string $usedCountMethod): array
    {
        $currentLimit = $user->getFeatureLimit($featureSlug);
        $usedCount = $user->$usedCountMethod()->count();
        
        $message = __('You have reached the maximum limit for creating :feature in your current plan. Please upgrade your plan to create more :feature.', [
            'feature' => $featureName
        ]);
        $details = __(':feature used', ['feature' => $featureName]) . ': ' . $usedCount . ' / ' . ($usedCount + $currentLimit);
        
        return [
            'type' => 'error',
            'message' => $message,
            'details' => $details,
            'options' => [
                'position' => 'center',
                'timer' => 8000,
                'toast' => false,
                'text' => $details,
                'showConfirmButton' => true,
                'confirmButtonText' => __('Upgrade Plan'),
                'confirmButtonColor' => '#3085d6',
                'showCancelButton' => true,
                'cancelButtonText' => __('Close'),
            ]
        ];
    }

    /**
     * Check if user can use feature and return alert data if not
     *
     * @param User $user
     * @param string $featureSlug
     * @return array|null
     */
    public static function checkFeatureAccess(User $user, string $featureSlug): ?array
    {
        if (!$user->canUseFeature($featureSlug)) {
            switch ($featureSlug) {
                case 'create-collection':
                    return self::getCollectionLimitAlert($user);
                case 'create-link-tree':
                    return self::getLinkTreeLimitAlert($user);
                case 'personal-profile':
                    return self::getProfileLimitAlert($user);
                default:
                    return [
                        'type' => 'error',
                        'message' => __('Feature limit exceeded'),
                        'details' => __('Please upgrade your subscription to continue'),
                        'options' => [
                            'position' => 'center',
                            'timer' => 5000,
                            'toast' => false,
                            'showConfirmButton' => true,
                            'confirmButtonText' => __('Upgrade Plan'),
                            'confirmButtonColor' => '#3085d6',
                            'showCancelButton' => true,
                            'cancelButtonText' => __('Close'),
                        ]
                    ];
            }
        }
        
        return null;
    }

    /**
     * Get subscription upgrade URL
     *
     * @return string
     */
    public static function getUpgradeUrl(): string
    {
        return route('subscriptions.subscribe');
    }

    /**
     * Generate subscription limit alert data for Profiles
     *
     * @param User $user
     * @return array
     */
    public static function getProfileLimitAlert(User $user): array
    {
        $currentLimit = $user->getFeatureLimit('personal-profile');
        $usedProfiles = $user->profiles()->count();
        
        $message = __('You have reached the maximum limit for creating Profiles in your current plan. Please upgrade your plan to create more Profiles.');
        $details = __('Profiles used') . ': ' . $usedProfiles . ' / ' . ($usedProfiles + $currentLimit);
        
        return [
            'type' => 'error',
            'message' => $message,
            'details' => $details,
            'options' => [
                'position' => 'center',
                'timer' => 8000,
                'toast' => false,
                'text' => $details,
                'showConfirmButton' => true,
                'confirmButtonText' => __('Upgrade Plan'),
                'confirmButtonColor' => '#3085d6',
                'showCancelButton' => true,
                'cancelButtonText' => __('Close'),
            ]
        ];
    }
}