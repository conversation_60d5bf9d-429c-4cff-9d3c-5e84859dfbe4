<?php

namespace App\Helpers;

class ServiceResult
{
    public bool $Status = true;
    public mixed $data = null;
    public int $Code = 200;
    public ?string $message = null;
    
    public function __construct(bool $status = true, mixed $data = null, int $code = 200, ?string $message = null)
    {
        $this->Status = $status;
        $this->data = $data;
        $this->Code = $code;
        $this->message = $message;
    }
}
