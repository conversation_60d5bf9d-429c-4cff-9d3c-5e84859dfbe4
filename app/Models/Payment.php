<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $fillable = [
        'user_id',
        'subscription_id',
        'plan_id',
        'payment_id',
        'gateway',
        'amount',
        'currency',
        'status',
        'type',
        'gateway_data',
        'metadata',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'gateway_data' => 'array',
        'metadata' => 'array',
        'paid_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function getGatewayNameAttribute(): string
    {
        return match($this->gateway) {
            'paypal' => 'PayPal',
            'stripe' => 'Stripe',
            'paymob' => 'Paymob',
            default => ucfirst($this->gateway)
        };
    }

    public function getStatusBadgeAttribute(): string
    {
        return match($this->status) {
            'completed' => '<span class="badge bg-success">مكتمل</span>',
            'pending' => '<span class="badge bg-warning">معلق</span>',
            'failed' => '<span class="badge bg-danger">فاشل</span>',
            'cancelled' => '<span class="badge bg-secondary">ملغي</span>',
            'refunded' => '<span class="badge bg-info">مسترد</span>',
            default => '<span class="badge bg-light">' . $this->status . '</span>'
        };
    }
}