<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Education extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTranslations;
    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }
    public $translatable = ['specialization', 'entity_name', 'note'];
    protected $fillable = [
        'user_id',
        'profile_id',
        'start',
        'end',
        'specialization',
        'entity_name',
        'note',
        'status',
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }
    public function scopeSearch($query, $term)
    {
        $query->where(function ($query) use ($term) {
            $query->where('title', 'like', "%$term%")
                ->orWhere('entity_name', 'like', "%$term%");
        });
    }
}
