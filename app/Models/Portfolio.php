<?php

namespace App\Models;

use App\Traits\MySlugHelper;
use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Sluggable\HasTranslatableSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Translatable\HasTranslations;

class Portfolio extends Model implements HasMedia
{
    use HasFactory;
    use SoftDeletes;
    use HasTranslatableSlug;
    use HasTranslations;
    use InteractsWithMedia;
    use Loggable;
    public $translatable = ['title', 'slug', 'content', 'project_name'];
    protected $fillable = [
        'user_id',
        'profile_id',
        'title',
        'slug',
        'content',
        'category_id',
        'client',
        'Project_date',
        'project_name',
        'Project_URL',
        'status',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function registerMediaCollections(): void
    {
        // $this->addMediaCollection('portfolios')->singleFile();
        $this->addMediaCollection('portfolios');
    }

    protected function generateNonUniqueSlug(): string
    {
        $slugField = $this->slugOptions->slugField;
        $slugString = $this->getSlugSourceString();

        $slug = $this->getTranslations($slugField)[$this->getLocale()] ?? null;

        $slugGeneratedFromCallable = is_callable($this->slugOptions->generateSlugFrom);
        $hasCustomSlug = $this->hasCustomSlugBeenUsed() && !empty($slug);
        $hasNonChangedCustomSlug = !$slugGeneratedFromCallable && !$this->slugIsBasedOnTitle() && !empty($slug);

        if ($hasCustomSlug || $hasNonChangedCustomSlug) {
            $slugString = $slug;
        }

        return MySlugHelper::slug($this->getSlugSourceString());
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }

    public function scopeSearch($query, $term)
    {
        $query->where(function ($query) use ($term) {
            $query->where('title', 'like', "%$term%")
                ->orWhere('content', 'like', "%$term%");
        });
    }
}
