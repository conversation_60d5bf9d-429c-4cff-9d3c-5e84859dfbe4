<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Support\Str;

class Theme extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'user_id', 'name', 'slug', 'version', 'author', 'description',
        'directory_path', 'view_file', 'type', 'is_default', 'is_active',
        'primary_color', 'second_color', 'font', 'background_color',
        'background_image', 'config_data', 'assets_manifest',
        'customizable_options', 'installation_date', 'last_updated'
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'config_data' => 'array',
        'assets_manifest' => 'array',
        'customizable_options' => 'array',
        'installation_date' => 'datetime',
        'last_updated' => 'datetime'
    ];

    protected $dates = [
        'installation_date',
        'last_updated',
        'deleted_at'
    ];

    // Boot method for auto-generating slug
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($theme) {
            if (empty($theme->slug)) {
                $theme->slug = Str::slug($theme->name);
            }
            $theme->installation_date = now();
        });

        static::updating(function ($theme) {
            $theme->last_updated = now();
        });
    }

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function profiles()
    {
        return $this->hasMany(Profile::class);
    }

    // Media Collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('preview')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif']);

        $this->addMediaCollection('assets')
            ->acceptsMimeTypes(['text/css', 'application/javascript', 'image/jpeg', 'image/png', 'image/gif', 'image/svg+xml']);
    }

    // Helper Methods
    public function getThemePath()
    {
        return public_path("themes/{$this->type}/{$this->slug}");
    }

    public function getThemeUrl()
    {
        return asset("themes/{$this->type}/{$this->slug}");
    }

    public function getViewPath()
    {
        return "themes.{$this->type}.{$this->slug}.views.{$this->getViewFileName()}";
    }

    public function getViewFileName()
    {
        return str_replace('.blade.php', '', $this->view_file);
    }

    public function getAssetUrl($type, $file)
    {
        return asset("themes/{$this->type}/{$this->slug}/assets/{$type}/{$file}");
    }

    public function getCssFiles()
    {
        return $this->assets_manifest['css'] ?? [];
    }

    public function getJsFiles()
    {
        return $this->assets_manifest['js'] ?? [];
    }

    public function getImageFiles()
    {
        return $this->assets_manifest['images'] ?? [];
    }

    public function getPreviewImageUrl()
    {
        // Check if theme has uploaded preview image
        if ($this->hasMedia('preview')) {
            return $this->getFirstMediaUrl('preview');
        }

        // Check for preview image in theme directory
        $previewPath = public_path("themes/{$this->type}/{$this->slug}/preview.jpg");
        if (file_exists($previewPath)) {
            return asset("themes/{$this->type}/{$this->slug}/preview.jpg");
        }

        // Return default preview based on theme colors
        return $this->generateDefaultPreview();
    }

    /**
     * Generate default preview image URL
     */
    private function generateDefaultPreview()
    {
        // For now, return a placeholder with theme colors
        $primaryColor = str_replace('#', '', $this->primary_color ?? '3498db');
        $secondaryColor = str_replace('#', '', $this->second_color ?? '2ecc71');

        return "https://via.placeholder.com/400x300/{$primaryColor}/{$secondaryColor}?text=" . urlencode($this->name);
    }

    public function isInstalled()
    {
        return file_exists($this->getThemePath());
    }

    public function getConfigData($key = null, $default = null)
    {
        if ($key) {
            return data_get($this->config_data, $key, $default);
        }
        return $this->config_data;
    }

    public function getCustomizableOption($key, $default = null)
    {
        return data_get($this->customizable_options, $key, $default);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeInstalled($query)
    {
        return $query->whereNotNull('installation_date');
    }
}
