<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Profile extends Model implements HasMedia
{
    use SoftDeletes, HasTranslations, InteractsWithMedia;

    protected $fillable = [
        'user_id', 'slug', 'name', 'bio', 'job_title', 'email', 'phone',
        'website', 'domain', 'address', 'theme_id', 'settings', 'seo_data',
        'visibility', 'is_active'
    ];

    protected $casts = [
        'name' => 'array',
        'bio' => 'array', 
        'job_title' => 'array',
        'address' => 'array',
        'settings' => 'array',
        'seo_data' => 'array',
        'is_active' => 'boolean'
    ];

    public $translatable = ['name', 'bio', 'job_title', 'address'];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function theme()
    {
        return $this->belongsTo(Theme::class);
    }

    public function skills()
    {
        return $this->hasMany(Skill::class);
    }

    public function education()
    {
        return $this->hasMany(Education::class);
    }

    public function experiences()
    {
        return $this->hasMany(Experience::class);
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

    public function portfolios()
    {
        return $this->hasMany(Portfolio::class);
    }

    public function socials()
    {
        return $this->hasMany(Social::class, 'relationship_id')->where('type', 'profile');
    }

    // Media Collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')->singleFile();
        $this->addMediaCollection('cover')->singleFile();
    }
}
