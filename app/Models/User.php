<?php

namespace App\Models;

use App\Traits\FeatureAccessTrait;
use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use Lara<PERSON>\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use SoftDeletes;
    use FeatureAccessTrait;
    use TwoFactorAuthenticatable;
    use Loggable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'phone',
        'role_id',
        'code',
        'code_expired_at',
        'profile_photo_path',
        'device_token',
        'default_language',
        'last_seen',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'code_expired_at' => 'datetime',
        'last_seen' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    // protected $appends = [
    //     'profile_photo_url',
    // ];

    // public function fcmTokens()
    // {
    //     // Check if the DeviceToken model exists before returning the relationship
    //     if (class_exists(DeviceToken::class)) {
    //         return $this->hasMany(DeviceToken::class, 'user_id');
    //     }

    //     return null; // or handle the case where the model does not exist
    // }

    public function routeNotificationForFcm()
    {
        return $this->fcmTokens ? $this->fcmTokens->pluck('token')->toArray() : [];
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function hasPermission($key)
    {
        return $this->role->permission->contains('key', $key);
    }

    // about relation
    public function about()
    {
        return $this->hasOne(About::class);
    }

    // add Skill relations
    public function skills()
    {
        return $this->hasMany(Skill::class);
    }
    // add Category relations

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

    // add Education relations
    public function educations()
    {
        return $this->hasMany(Education::class);
    }

    // add Experience relations
    public function experiences()
    {
        return $this->hasMany(Experience::class);
    }
    // add Social relations

    public function socials()
    {
        return $this->hasMany(Social::class)->where('is_active', true)->where('type', 'profile');
    }
    // add Social relations

    public function linkTrees()
    {
        return $this->hasMany(LinkTree::class);
    }
    // get collection relations

    public function collections()
    {
        return $this->hasMany(Collection::class);
    }

    // get profiles relations
    public function profiles()
    {
        return $this->hasMany(Profile::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class)
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->latest();
    }

    public function getActiveSubscriptionAttribute()
    {
        return $this->activeSubscription()->first();
    }

    public function featureUsages()
    {
        return $this->hasMany(FeatureUsage::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function completedPayments()
    {
        return $this->hasMany(Payment::class)->where('status', 'completed');
    }
}
