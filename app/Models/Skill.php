<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Skill extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTranslations;
    protected $fillable = [
        'user_id',
        'profile_id',
        'key',
        'value',
        'status',
    ];
    public $translatable = ['key', 'value'];
    use HasFactory;    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }
    public function scopeSearch($query, $term)
    {
        $query->where(function ($query) use ($term) {
            $query->where('key', 'like', "%$term%")
                ->orWhere('value', 'like', "%$term%");
        });
    }
}
