<?php

namespace App\Models;

use App\Traits\MySlugHelper;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sluggable\HasTranslatableSlug;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Category extends Model
{
    use HasFactory;
    use HasTranslations;
    use HasTranslatableSlug;
    use SoftDeletes;
    protected $fillable = ['user_id', 'profile_id', 'name', 'slug', 'parent_id', 'status', 'image'];
    public $translatable = ['name', 'slug'];
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }
    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected function generateNonUniqueSlug(): string
    {
        $slugField = $this->slugOptions->slugField;
        $slugString = $this->getSlugSourceString();

        $slug = $this->getTranslations($slugField)[$this->getLocale()] ?? null;

        $slugGeneratedFromCallable = is_callable($this->slugOptions->generateSlugFrom);
        $hasCustomSlug = $this->hasCustomSlugBeenUsed() && !empty($slug);
        $hasNonChangedCustomSlug = !$slugGeneratedFromCallable && !$this->slugIsBasedOnTitle() && !empty($slug);

        if ($hasCustomSlug || $hasNonChangedCustomSlug) {
            $slugString = $slug;
        }

        return MySlugHelper::slug($this->getSlugSourceString());
    }
    public function scopeSearch($query, $term)
    {
        $query->where(function ($query) use ($term) {
            $query->where('name', 'like', "%$term%");
        });
    }
    public function parent(){
        // return $this->parent_id != null ? $this->belongsTo(Category::class, 'parent_id', "id") : null;
        return $this->belongsTo(Category::class, 'parent_id', "id");
    }
    public function portfolio()
    {
        return $this->hasMany(Portfolio::class, 'category_id', "id")->where('status',true);
    }

    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }
}
