<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HandleAuthErrors
{
    public function handle(Request $request, Closure $next)
    {
        try {
            return $next($request);
        } catch (\Exception $e) {
            // إذا كان الخطأ متعلق بقاعدة البيانات أو النظام
            if (str_contains($e->getMessage(), 'SQLSTATE') || 
                str_contains($e->getMessage(), 'foreign key constraint')) {
                
                Log::error('Auth Error: ' . $e->getMessage());
                
                // إذا كان طلب AJAX أو Livewire
                if ($request->wantsJson() || $request->header('X-Livewire')) {
                    return response()->json([
                        'error' => __('An error occurred. Please try again.')
                    ], 500);
                }
                
                // إعادة توجيه مع رسالة خطأ
                return redirect()->back()
                    ->withInput($request->except('password'))
                    ->withErrors(['email' => __('An error occurred. Please try again.')]);
            }
            
            throw $e;
        }
    }
}