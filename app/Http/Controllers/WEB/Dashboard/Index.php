<?php

namespace App\Http\Controllers\Web\Dashboard;

use App\Http\Controllers\WEB\LivewireController;
use App\Models\Feature;
use App\Models\LinkTree;
use App\Models\Plan;
use App\Models\Portfolio;
use App\Models\Project;
use App\Models\User;
use App\Services\AnalyticsService;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class Index extends LivewireController
{
    use LivewireAlert;
    
    public $stats = [];
    public $subscription;
    public $plans = [];
    public $features = [];
    public $userFeatureUsage = [];
    public $subscriptionLimits = [];
    public $analyticsData = [];
    public $isAdmin = false;

    protected $analyticsService;

    public function boot()
    {
        $this->analyticsService = new AnalyticsService();
    }

    public function mount()
    {
        $user = Auth::user();
        $this->isAdmin = $user->role_id === 1;

        $this->loadBasicStats($user);
        $this->loadAnalyticsData($user);

        if (!$this->isAdmin) {
            $this->loadUserSpecificData($user);
        }
        
        // Handle PayPal return parameters
        $request = request();
        if ($request->has('token') && $request->has('PayerID')) {
            $this->handlePayPalReturn($request);
        }
        
        // Handle LivewireAlert from session
        if (session('livewire_alert')) {
            $alert = session('livewire_alert');
            $this->alert($alert['type'], $alert['title'], [
                'text' => $alert['message'],
                'position' => 'top-end',
                'timer' => 5000,
                'toast' => true,
                'showConfirmButton' => false
            ]);
            session()->forget('livewire_alert');
        }
    }

    public function refreshAnalytics()
    {
        $user = Auth::user();
        $this->loadBasicStats($user);
        $this->loadAnalyticsData($user);

        if (!$this->isAdmin) {
            $this->loadUserSpecificData($user);
        }

        $this->dispatch('analytics-refreshed');
    }

    private function loadBasicStats($user)
    {
        if ($this->isAdmin) {
            // Admin stats
            $this->stats = [
                'total_users' => User::count(),
                'link_trees' => LinkTree::count(),
                'portfolios' => Portfolio::count(),
                'projects' => Project::count(),
            ];
        } else {
            // User stats
            $this->stats = [
                'link_trees' => $user->linkTrees()->count(),
                'portfolios' => Portfolio::where('user_id', $user->id)->count(),
                'projects' => Project::where('user_id', $user->id)->count(),
            ];

            // Load subscription limits if user has subscription
            if ($this->subscription) {
                $this->loadSubscriptionLimits();
            }
        }
    }

    private function loadSubscriptionLimits()
    {
        $this->subscriptionLimits = [];

        foreach ($this->subscription->plan->features as $feature) {
            $featureName = $feature->getTranslation('name', app()->getLocale());
            $limitType = $feature->pivot->limit_type_id ? \App\Models\LimitType::find($feature->pivot->limit_type_id) : null;
            $limitName = $limitType ? $limitType->getTranslation('name', app()->getLocale()) : '';

            $this->subscriptionLimits[] = [
                'name' => $featureName,
                'limit' => $feature->pivot->allowed_limit,
                'limit_type' => $limitName,
                'used' => $this->getFeatureUsage($feature->id, auth()->id()),
            ];
        }
    }

    private function getFeatureUsage($featureId, $userId)
    {
        // Get actual usage based on feature type
        $feature = Feature::find($featureId);
        if (!$feature) {
            return 0;
        }

        $featureName = strtolower($feature->getTranslation('name', 'en'));

        if (str_contains($featureName, 'linktree') || str_contains($featureName, 'link tree')) {
            return LinkTree::where('user_id', $userId)->count();
        }

        return 0;
    }

    private function loadAnalyticsData($user)
    {
        try {
            $analyticsResult = $this->isAdmin
                ? $this->analyticsService->getAdminDashboardStats()
                : $this->analyticsService->getUserDashboardStats($user->id);

            if ($analyticsResult->Status) {
                $this->analyticsData = $analyticsResult->data;
            } else {
                $this->analyticsData = $this->getEmptyAnalyticsData();
            }
        } catch (\Exception $e) {
            $this->analyticsData = $this->getEmptyAnalyticsData();
        }
    }

    private function getEmptyAnalyticsData()
    {
        if ($this->isAdmin) {
            // For admin, provide basic counts even when analytics fail
            return [
                'total_users' => User::count(),
                'active_subscriptions' => User::whereHas('activeSubscription')->count(),
                'total_linktrees' => LinkTree::count(),
                'total_visits_today' => 0,
                'unique_visits_today' => 0,
                'total_clicks_today' => 0,
                'visits_chart' => $this->generateSampleChartData(),
                'users_chart' => $this->generateSampleUsersChartData(),
                'subscriptions_chart' => $this->generateSampleSubscriptionsChartData(),
                'popular_pages' => [],
                'recent_users' => User::latest()->take(5)->get(),
                'top_linktrees' => [],
                'plans_distribution' => [],
                'revenue_stats' => [],
            ];
        } else {
            return [
                'total_views' => 0,
                'total_views_today' => 0,
                'unique_visitors' => 0,
                'unique_visitors_today' => 0,
                'total_clicks_today' => 0,
                'total_clicks' => 0,
                'total_linktrees' => 0,
                'views_chart' => [],
                'link_performance' => [],
            ];
        }
    }

    private function generateSampleChartData()
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'total_visits' => rand(5, 25),
                'unique_visits' => rand(3, 15),
            ];
        }
        return $data;
    }

    private function generateSampleUsersChartData()
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'registrations' => rand(0, 5),
            ];
        }
        return $data;
    }

    private function generateSampleSubscriptionsChartData()
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'new_subscriptions' => rand(0, 3),
            ];
        }
        return $data;
    }

    private function loadUserSpecificData($user)
    {
        $this->subscription = $user->activeSubscription;
        $this->plans = Plan::where('is_active', true)->where('is_public', true)->get();
        $this->features = Feature::where('is_active', true)->get();

        $this->userFeatureUsage = $user->featureUsages()
                                      ->with('feature')
                                      ->get()
                                      ->keyBy(function ($item) {
                                          return $item->feature ? $item->feature->getTranslation('name', app()->getLocale()) : 'unknown';
                                      });
    }

    private function handlePayPalReturn($request)
    {
        \Log::info('PayPal Return Detected in Dashboard', [
            'request_data' => $request->all(),
            'session_data' => session()->all()
        ]);
        
        $token = $request->get('token');
        $payerId = $request->get('PayerID');
        
        if (!$token || !$payerId) {
            return;
        }
        
        try {
            $paymentService = new \App\Services\PaymentService();
            $result = $paymentService->verifyPayment($request);
            
            if ($result->Status) {
                $planId = session('pending_plan_id');
                if ($planId) {
                    $user = Auth::user();
                    
                    // Cancel existing subscription if any
                    $existingSubscription = $user->activeSubscription;
                    if ($existingSubscription) {
                        $existingSubscription->update([
                            'status' => 'canceled',
                            'ends_at' => now()
                        ]);
                        \Log::info('Cancelled existing subscription', ['subscription_id' => $existingSubscription->id]);
                    }
                    
                    $subscriptionService = new \App\Services\SubscriptionService();
                    $plan = \App\Models\Plan::find($planId);
                    $subscriptionResult = $subscriptionService->subscribeUserToPlan(
                        $user,
                        $planId,
                        [
                            'starts_at' => now(),
                            'auto_renew' => true,
                            'payment_data' => $result->data,
                            'billing_interval' => $plan->billing_interval,
                            'billing_interval_count' => $plan->billing_interval_count
                        ]
                    );
                    
                    \Log::info('Subscription Service Result', [
                        'status' => $subscriptionResult->Status,
                        'data' => $subscriptionResult->data
                    ]);
                    
                    if ($subscriptionResult->Status) {
                        // ربط الدفع بالاشتراك
                        $paymentId = $result->data['payment_id'] ?? $token;
                        \App\Models\Payment::where('payment_id', $paymentId)->update([
                            'subscription_id' => $subscriptionResult->data->id
                        ]);
                        \Log::info('Payment linked to subscription in dashboard', [
                            'payment_id' => $paymentId,
                            'subscription_id' => $subscriptionResult->data->id
                        ]);
                        
                        session()->forget('pending_plan_id');
                        
                        $plan = \App\Models\Plan::find($planId);
                        $planName = $plan ? $plan->getTranslation('name', app()->getLocale()) : __('Plan');
                        
                        $this->alert('success', __('Subscription Successful!'), [
                            'text' => __('You have successfully subscribed to :plan', ['plan' => $planName]),
                            'position' => 'top-end',
                            'timer' => 5000,
                            'toast' => true,
                            'showConfirmButton' => false
                        ]);
                        
                        // Refresh data
                        $this->loadUserSpecificData($user);
                        return;
                    }
                }
            }
            
            $this->alert('error', __('Payment Failed'), [
                'text' => __('Payment verification failed'),
                'position' => 'top-end',
                'timer' => 5000,
                'toast' => true,
                'showConfirmButton' => false
            ]);
            
        } catch (\Exception $e) {
            \Log::error('PayPal Return Handler Error: ' . $e->getMessage());
            $this->alert('error', __('Payment Error'), [
                'text' => __('An error occurred while processing payment'),
                'position' => 'top-end',
                'timer' => 5000,
                'toast' => true,
                'showConfirmButton' => false
            ]);
        }
    }

    public function render()
    {
        return view('pages.dashboard.index')->layout('layouts.app');
    }
}
