<?php

namespace App\Http\Controllers\WEB\Front\V1;

use App\Services\Front\ProfileService;
use Livewire\Component;

class ProfessionalProfile extends Component
{
    public $slug;
    public $profile;
    protected $profileService;

    public function boot(ProfileService $profileService)
    {
        $this->profileService = $profileService;
    }

    public function mount($slug)
    {
        $this->slug = $slug;
        $this->profile = $this->profileService->getPublicProfileBySlug($slug);

        if (!$this->profile) {
            abort(404);
        }
    }

    public function render()
    {
        $description = $this->profile->getTranslation('bio', app()->getLocale()) ?: 'Professional Profile';
        $keywords = $this->profile->getTranslation('name', app()->getLocale()) ?: 'Profile';
        $site_logo = $this->profile->getFirstMediaUrl('avatar') ?: asset('assets/images/default-avatar.png');
        
        return view('pages.front.v1.professional-profile', [
            'profile' => $this->profile,
        ])->layout('layouts.profile-v1', [
            'profile' => $this->profile,
            'description' => $description,
            'keywords' => $keywords,
            'site_logo' => $site_logo,
        ]);
    }
}
