<?php

namespace App\Http\Controllers\WEB\Front\V1;

use App\Models\About;
use App\Models\Message;
use App\Models\User;
use App\Services\FCMService;
use App\Services\Front\HomeService;
use App\Services\UserService;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Index extends Component
{
    use LivewireAlert;

    protected $UserService;
    protected $HomeService;

    public string $name = '';
    public string $email = '';
    public string $phone = '';
    public string $message = '';

    public function __construct()
    {
        $this->UserService = new UserService();
        $this->HomeService = new HomeService();
    }

    public function getItem()
    {
        // Get complete home page data including plans
        $completeDataResult = $this->HomeService->getCompleteHomeData();
        $home = null;
        $plans = [];

        if ($completeDataResult->Status) {
            $home = $completeDataResult->data['home'];
            $plans = $completeDataResult->data['plans'];
        }

        // Get additional data for the page
        $about = $home;
        $socials = [];
        $categories = [];
        $skills = [];
        $experiences = [];
        $educations = [];

        if ($about) {
            $socials = $about->socials ?? [];
            $categories = $about->categories ?? [];
            $skills = $about->skills ?? [];
            $experiences = $about->experiences ?? [];
            $educations = $about->educations ?? [];
        }

        return [
            'home' => $home,
            'about' => $about,
            'plans' => $plans,
            'socials' => $socials,
            'categories' => $categories,
            'skills' => $skills,
            'experiences' => $experiences,
            'educations' => $educations,
        ];
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|min:3|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'required|string|min:8|max:20',
            'message' => 'required|string|min:10',
        ];
    }

    public function create()
    {
        $user = User::find(1);
        $settings = About::find(1);
        $this->validate();
        $data = [
            'user_id' => $user->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'content' => $this->message,
        ];

        $message = Message::create($data);

        $notification = [
            'title' => __('رسالة جديدة من :').' '.$this->name,
            'body' => __('الهاتف').': '.$this->phone."\n".__('البريد').': '.$this->email."\n".__('محتوى الرسالة').': '.$this->message,
        ];

        $deviceToken = $user->device_token;

        FCMService::sendToDevice($deviceToken, $notification, $notification, $settings->image);
        $this->dispatch('refreshParent');
        $this->resetValidation();
        $this->resetErrorBag();
        $this->reset(['name', 'email', 'phone', 'message']);
        $this->alert('success', __('Message Sent Successfully'), ['position' => 'center']);
    }

    public function payment_verify($payment = null)
    {
        // التحقق من إلغاء الدفع
        if (request()->has('cancel')) {
            return redirect()->route('dashboard')->with('error', 'تم إلغاء عملية الدفع');
        }
        
        $paymentService = new \App\Services\PaymentService();
        $result = $paymentService->verifyPayment(request());
        
        if ($result->Status) {
            // إذا نجح الدفع، إكمال الاشتراك
            $planId = session('pending_plan_id');
            if ($planId && auth()->check()) {
                $subscriptionService = new \App\Services\SubscriptionService();
                $subscriptionResult = $subscriptionService->subscribeUserToPlan(
                    auth()->user(),
                    $planId,
                    [
                        'starts_at' => now(),
                        'auto_renew' => true,
                        'payment_data' => $result->data
                    ]
                );
                
                session()->forget('pending_plan_id');
                
                if ($subscriptionResult->Status) {
                    return redirect()->route('dashboard')->with('success', 'تم الدفع والاشتراك بنجاح');
                }
            }
            
            return redirect()->route('dashboard')->with('success', 'تم الدفع بنجاح');
        }
        
        return redirect()->route('dashboard')->with('error', $result->data);
    }

    public function render()
    {
        return view('pages.front.v1.index', [
            'homePage' => $this->getItem(),
        ])->layout('layouts.v1');
    }
}
