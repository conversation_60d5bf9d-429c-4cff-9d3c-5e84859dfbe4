<?php

namespace App\Http\Controllers\WEB\Themes;

use App\Http\Controllers\WEB\LivewireController;
use App\Services\ThemeService;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;

class Index extends LivewireController
{
    use LivewireAlert;

    protected $themeService;

    // Filter properties
    public $selectedType = '';
    public $selectedStatus = '';
    public $showOnlyDefault = false;

    // Action properties
    public $selectedThemeId = null;
    public $showDeleteModal = false;
    public $showSetDefaultModal = false;

    protected $listeners = [
        'refreshParent' => '$refresh',
        'showDeleteModal' => 'showDeleteModal',
        'showSetDefaultModal' => 'showSetDefaultModal',
        'confirmDelete' => 'deleteTheme',
        'confirmSetDefault' => 'setAsDefault'
    ];

    public function boot(ThemeService $themeService)
    {
        $this->themeService = $themeService;
    }

    public function mount()
    {
        // Initialize any required data
        $this->loadItems();
    }

    public function updatingTerm()
    {
        parent::updatingTerm();
    }

    public function updatingOrderBy()
    {
        parent::updatingOrderBy();
    }

    public function updatingSortBy()
    {
        parent::updatingSortBy();
    }

    public function updatingPerPage()
    {
        parent::updatingPerPage();
    }

    public function updatingTrashed()
    {
        parent::updatingTrashed();
    }

    public function updatingSelectedType()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingShowOnlyDefault()
    {
        $this->resetPage();
    }

    public function toggleTrashed()
    {
        parent::toggleTrashed();
    }

    public function getItem()
    {
        $filters = [
            'type' => $this->selectedType,
            'status' => $this->selectedStatus,
            'showOnlyDefault' => $this->showOnlyDefault
        ];

        if (!empty($this->term)) {
            return $this->themeService->searchThemes($this->term, $this->perPage)->data;
        } else {
            return $this->themeService->allThemes($this->perPage, $this->trashed, $this->orderBy, $this->sortBy, $filters)->data;
        }
    }

    public function selectedItem($action, $itemId = null)
    {
        switch ($action) {
            case 'create':
                return redirect()->route('themes.create');

            case 'upload':
                return redirect()->route('themes.upload');

            case 'edit':
                return redirect()->route('themes.edit', $itemId);

            case 'customize':
                return redirect()->route('themes.customize', $itemId);

            case 'preview':
                return redirect()->route('themes.preview', $itemId);

            case 'delete':
                $this->showDeleteModal($itemId);
                break;

            case 'setDefault':
                $this->showSetDefaultModal($itemId);
                break;

            case 'toggleStatus':
                $this->toggleThemeStatus($itemId);
                break;

            case 'restore':
                $this->restoreTheme($itemId);
                break;

            case 'forceDelete':
                $this->forceDeleteTheme($itemId);
                break;
        }
    }

    public function showDeleteModal($themeId)
    {
        $this->selectedThemeId = $themeId;
        $this->showDeleteModal = true;
        $this->dispatch('show-delete-modal');
    }

    public function showSetDefaultModal($themeId)
    {
        $this->selectedThemeId = $themeId;
        $this->showSetDefaultModal = true;
        $this->dispatch('show-set-default-modal');
    }

    public function deleteTheme()
    {
        if (!$this->selectedThemeId) {
            $this->alert('error', 'لم يتم تحديد الثيم');
            return;
        }

        $result = $this->themeService->deleteTheme($this->selectedThemeId);

        if ($result->Status) {
            $this->alert('success', $result->Message);
            $this->resetPage();
        } else {
            $this->alert('error', $result->Message);
        }

        $this->showDeleteModal = false;
        $this->selectedThemeId = null;
    }

    public function setAsDefault()
    {
        if (!$this->selectedThemeId) {
            $this->alert('error', 'لم يتم تحديد الثيم');
            return;
        }

        $result = $this->themeService->setAsDefault($this->selectedThemeId);

        if ($result->Status) {
            $this->alert('success', $result->Message);
            $this->resetPage();
        } else {
            $this->alert('error', $result->Message);
        }

        $this->showSetDefaultModal = false;
        $this->selectedThemeId = null;
    }

    public function toggleThemeStatus($themeId)
    {
        $themeResult = $this->themeService->singleTheme($themeId);

        if (!$themeResult->Status) {
            $this->alert('error', 'الثيم غير موجود');
            return;
        }

        $theme = $themeResult->data;
        $newStatus = !$theme->is_active;

        $result = $this->themeService->updateTheme($themeId, ['is_active' => $newStatus]);

        if ($result->Status) {
            $message = $newStatus ? 'تم تفعيل الثيم بنجاح' : 'تم إلغاء تفعيل الثيم بنجاح';
            $this->alert('success', $message);
            $this->resetPage();
        } else {
            $this->alert('error', $result->Message);
        }
    }

    public function restoreTheme($themeId)
    {
        // This would be implemented when we add restore functionality
        $this->alert('info', 'سيتم تنفيذ هذه الميزة قريباً');
    }

    public function forceDeleteTheme($themeId)
    {
        // This would be implemented when we add force delete functionality
        $this->alert('info', 'سيتم تنفيذ هذه الميزة قريباً');
    }

    public function getThemeTypes()
    {
        return [
            '' => 'جميع الأنواع',
            'profile' => 'البروفايل',
            'link_tree' => 'لينك تري',
            'company' => 'الشركات',
            'product' => 'المنتجات',
            'store' => 'المتاجر',
            'project' => 'المشاريع'
        ];
    }

    public function getStatusOptions()
    {
        return [
            '' => 'جميع الحالات',
            'active' => 'نشط',
            'inactive' => 'غير نشط'
        ];
    }

    public function resetFilters()
    {
        $this->selectedType = '';
        $this->selectedStatus = '';
        $this->showOnlyDefault = false;
        $this->term = '';
        $this->resetPage();
    }

    public function render()
    {
        return view('pages.themes.index', [
            'themes' => $this->getItem(),
            'themeTypes' => $this->getThemeTypes(),
            'statusOptions' => $this->getStatusOptions()
        ])->layout('layouts.app');
    }
}
