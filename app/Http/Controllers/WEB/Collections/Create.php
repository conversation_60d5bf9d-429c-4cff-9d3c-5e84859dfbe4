<?php

namespace App\Http\Controllers\WEB\Collections;

use App\Models\Collection;
use App\Services\CollectionService;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Create extends Component
{
    use LivewireAlert;
    protected $CollectionService;
    public $collectionName = '';
    public $authorization = false;
    public $arrays = [];
    public $transformedJson = [];
    public $authpassword;

    public function boot(CollectionService $CollectionService)
    {
        $this->CollectionService = $CollectionService;
    }
    protected $rules = [
        'collectionName' => 'required|min:3|unique:collections,name',
        'arrays' => 'required|array',
        'arrays.*.name' => 'required|string',
        'arrays.*.keys' => 'required|array',
        'arrays.*.values' => 'required|array',
    ];

    protected function messages()
    {
        return [
            'collectionName.required' => __('The collection name field is required.'),
            'collectionName.min' => __('The collection name must be at least 3 characters.'),
            'collectionName.unique' => __('The collection name has already been taken.'),
            'arrays.required' => __('The arrays field is required.'),
            'arrays.array' => __('The arrays must be an array.'),
            'arrays.*.name.required' => __('The array name is required.'),
            'arrays.*.name.string' => __('The array name must be a string.'),
            'arrays.*.keys.required' => __('The array keys field is required.'),
            'arrays.*.keys.array' => __('The array keys must be an array.'),
            'arrays.*.values.required' => __('The array values field is required.'),
            'arrays.*.values.array' => __('The array values must be an array.'),
        ];
    }

    public function mount()
    {
        $this->updateTransformedJson();
    }

    public function updateTransformedJson()
    {
        $this->transformedJson = [];

        foreach ($this->arrays as $array) {
            if (empty($array['name'])) {
                continue;
            }

            $arrayData = [];
            foreach ($array['keys'] as $index => $key) {
                if (!empty($key) && isset($array['values'][$index])) {
                    $arrayData[$key] = $array['values'][$index];
                }
            }

            if (isset($array['subarrays']) && !empty($array['subarrays'])) {
                foreach ($array['subarrays'] as $subarray) {
                    if (empty($subarray['name'])) {
                        continue;
                    }

                    $subarrayData = [];

                    foreach ($subarray['keys'] as $index => $key) {
                        if (!empty($key) && isset($subarray['values'][$index])) {
                            $subarrayData[$key] = $subarray['values'][$index];
                        }
                    }

                    $arrayData[$subarray['name']] = $subarrayData;
                }
            }

            $this->transformedJson[$array['name']] = $arrayData;
        }
    }

    public function updated()
    {
        $this->updateTransformedJson();
    }

    public function addArray()
    {
        $this->arrays[] = [
            'name' => '',
            'keys' => [''],
            'values' => [''],
            'subarrays' => [],
        ];
        $this->updateTransformedJson();
    }

    public function addKeyValue($arrayIndex)
    {
        $this->arrays[$arrayIndex]['keys'][] = '';
        $this->arrays[$arrayIndex]['values'][] = '';
        $this->updateTransformedJson();
    }

    public function removeArray($arrayIndex)
    {
        unset($this->arrays[$arrayIndex]);
        $this->arrays = array_values($this->arrays);
        $this->updateTransformedJson();
    }

    public function removeKeyValue($arrayIndex, $keyIndex)
    {
        unset($this->arrays[$arrayIndex]['keys'][$keyIndex]);
        unset($this->arrays[$arrayIndex]['values'][$keyIndex]);

        $this->arrays[$arrayIndex]['keys'] = array_values($this->arrays[$arrayIndex]['keys']);
        $this->arrays[$arrayIndex]['values'] = array_values($this->arrays[$arrayIndex]['values']);
        $this->updateTransformedJson();
    }

    public function addSubArray($arrayIndex)
    {
        if (!isset($this->arrays[$arrayIndex]['subarrays'])) {
            $this->arrays[$arrayIndex]['subarrays'] = [];
        }
        $this->arrays[$arrayIndex]['subarrays'][] = [
            'name' => '',
            'keys' => [''],
            'values' => [''],
        ];
        $this->updateTransformedJson();
    }

    public function removeSubArray($arrayIndex, $subArrayIndex)
    {
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]);
        $this->arrays[$arrayIndex]['subarrays'] = array_values($this->arrays[$arrayIndex]['subarrays']);
        $this->updateTransformedJson();
    }

    public function addSubKeyValue($arrayIndex, $subArrayIndex)
    {
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'][] = '';
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'][] = '';
        $this->updateTransformedJson();
    }

    public function removeSubKeyValue($arrayIndex, $subArrayIndex, $keyIndex)
    {
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'][$keyIndex]);
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'][$keyIndex]);

        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'] = array_values($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys']);
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'] = array_values($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values']);
        $this->updateTransformedJson();
    }

    // generatePassword function
    public function generatePassword()
    {
        $this->authpassword = $this->CollectionService->generatePassword();
    }

    public function save()
    {
        $this->validate();
        $data = [
            'user_id' => auth()->user()->id,
            'name' => $this->collectionName,
            'data' => $this->transformedJson,
            'authorization' => $this->authorization,
            'password' => $this->authpassword,
            'status' => true,
        ];
        $result = $this->CollectionService->createCollection($data);
        if ($result->Status) {
            // تسجيل استخدام الميزة
            $user = auth()->user();
            $user->recordFeatureUsage('create-collection');
            // if ($this->social_icon) {
            //     $result->data->addMedia($this->social_icon)->toMediaCollection('social_icon');
            // }
            // $this->closeCreateModel();
            // $this->alert('success', __('Social Created successfully'));
            // $this->dispatch('refreshParent');

            // $data = $this->transformedJson;

            // Collection::create([
            //     'user_id' => auth()->user()->id,
            //     'name' => $this->collectionName,
            //     'data' => $data,
            //     'authorization' => $this->authorization,
            // ]);
            // $result = $this->pageService->updatePage($this->page->id, [
            //     'title' => $this->title,
            //     'content' => $this->content,
            //     // 'status' => $this->status,
            // ]);
            $this->alert('success', __('Collection created successfully'), [
                'position' => 'center',
                'timer' => 3000,
                'toast' => true,
            ]);
            $this->reset();
            $this->updateTransformedJson();

            return redirect()->route('collections.index');
        } else {
            $this->alert('error', __('Failed to create collection'), [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
            ]);
        }
    }

    public function render()
    {
        return view('pages.collections.create')->layout('layouts.app');
    }
}
