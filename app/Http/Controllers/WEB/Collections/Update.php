<?php

namespace App\Http\Controllers\WEB\Collections;

use App\Models\Collection;
use App\Services\CollectionService;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Update extends Component
{
    use LivewireAlert;
    protected $CollectionService;
    public $collection;
    public $collectionName = '';
    public $authorization = false;
    public $arrays = [];
    public $transformedJson = [];
    public $authpassword;

    public function boot(CollectionService $CollectionService)
    {
        $this->CollectionService = $CollectionService;
    }

    protected function rules()
    {
        return [
            'collectionName' => 'required|min:3|unique:collections,name,'.$this->collection->id,
            'arrays' => 'required|array',
            'arrays.*.name' => 'required|string',
            'arrays.*.keys' => 'required|array',
            'arrays.*.values' => 'required|array',
        ];
    }

    protected function messages()
    {
        return [
            'collectionName.required' => __('The collection name field is required.'),
            'collectionName.min' => __('The collection name must be at least 3 characters.'),
            'collectionName.unique' => __('The collection name has already been taken.'),
            'arrays.required' => __('The arrays field is required.'),
            'arrays.array' => __('The arrays must be an array.'),
            'arrays.*.name.required' => __('The array name is required.'),
            'arrays.*.name.string' => __('The array name must be a string.'),
            'arrays.*.keys.required' => __('The array keys field is required.'),
            'arrays.*.keys.array' => __('The array keys must be an array.'),
            'arrays.*.values.required' => __('The array values field is required.'),
            'arrays.*.values.array' => __('The array values must be an array.'),
        ];
    }

    public function mount($id)
    {
        $this->collection = Collection::findOrFail($id);
        $this->collectionName = $this->collection->name;
        $this->authorization = $this->collection->authorization;
        $this->authpassword = $this->collection->password;

        // Transform the stored data back to the arrays format
        $storedData = $this->collection->data ?? [];
        foreach ($storedData as $name => $data) {
            $array = [
                'name' => $name,
                'keys' => [],
                'values' => [],
                'subarrays' => [],
            ];

            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    // Handle subarrays
                    $subarray = [
                        'name' => $key,
                        'keys' => [],
                        'values' => [],
                    ];
                    foreach ($value as $subKey => $subValue) {
                        $subarray['keys'][] = $subKey;
                        $subarray['values'][] = $subValue;
                    }
                    $array['subarrays'][] = $subarray;
                } else {
                    $array['keys'][] = $key;
                    $array['values'][] = $value;
                }
            }
            $this->arrays[] = $array;
        }

        $this->updateTransformedJson();
    }

    // تحويل بنية البيانات للعرض مع الحفاظ على الترتيب
    public function updateTransformedJson()
    {
        $this->transformedJson = [];
        $orderedArrays = [];

        foreach ($this->arrays as $arrayIndex => $array) {
            if (empty($array['name'])) {
                continue;
            }

            $arrayData = [];

            // إضافة أزواج المفاتيح والقيم
            foreach ($array['keys'] as $index => $key) {
                if (!empty($key) && isset($array['values'][$index])) {
                    $arrayData[$key] = $array['values'][$index];
                }
            }

            // إضافة المصفوفات الفرعية
            if (isset($array['subarrays']) && !empty($array['subarrays'])) {
                foreach ($array['subarrays'] as $subarray) {
                    if (empty($subarray['name'])) {
                        continue;
                    }

                    $subarrayData = [];

                    foreach ($subarray['keys'] as $index => $key) {
                        if (!empty($key) && isset($subarray['values'][$index])) {
                            $subarrayData[$key] = $subarray['values'][$index];
                        }
                    }

                    $arrayData[$subarray['name']] = $subarrayData;
                }
            }

            // استخدام فهرس عددي للحفاظ على الترتيب
            $orderedArrays[$arrayIndex] = [
                'name' => $array['name'],
                'data' => $arrayData,
            ];
        }

        // إعادة بناء الكائن النهائي بالترتيب الصحيح
        foreach ($orderedArrays as $arrayData) {
            $this->transformedJson[$arrayData['name']] = $arrayData['data'];
        }
    }

    public function generatePassword()
    {
        $this->authpassword = $this->CollectionService->generatePassword();
    }

    public function updated()
    {
        $this->updateTransformedJson();
    }

    public function addArray()
    {
        $this->arrays[] = [
            'name' => '',
            'keys' => [''],
            'values' => [''],
            'subarrays' => [],
        ];
        $this->updateTransformedJson();
    }

    public function addKeyValue($arrayIndex)
    {
        $this->arrays[$arrayIndex]['keys'][] = '';
        $this->arrays[$arrayIndex]['values'][] = '';
        $this->updateTransformedJson();
    }

    public function removeArray($arrayIndex)
    {
        unset($this->arrays[$arrayIndex]);
        $this->arrays = array_values($this->arrays);
        $this->updateTransformedJson();
    }

    public function removeKeyValue($arrayIndex, $keyIndex)
    {
        unset($this->arrays[$arrayIndex]['keys'][$keyIndex]);
        unset($this->arrays[$arrayIndex]['values'][$keyIndex]);

        $this->arrays[$arrayIndex]['keys'] = array_values($this->arrays[$arrayIndex]['keys']);
        $this->arrays[$arrayIndex]['values'] = array_values($this->arrays[$arrayIndex]['values']);
        $this->updateTransformedJson();
    }

    public function addSubArray($arrayIndex)
    {
        if (!isset($this->arrays[$arrayIndex]['subarrays'])) {
            $this->arrays[$arrayIndex]['subarrays'] = [];
        }
        $this->arrays[$arrayIndex]['subarrays'][] = [
            'name' => '',
            'keys' => [''],
            'values' => [''],
        ];
        $this->updateTransformedJson();
    }

    public function removeSubArray($arrayIndex, $subArrayIndex)
    {
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]);
        $this->arrays[$arrayIndex]['subarrays'] = array_values($this->arrays[$arrayIndex]['subarrays']);
        $this->updateTransformedJson();
    }

    public function addSubKeyValue($arrayIndex, $subArrayIndex)
    {
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'][] = '';
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'][] = '';
        $this->updateTransformedJson();
    }

    public function removeSubKeyValue($arrayIndex, $subArrayIndex, $keyIndex)
    {
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'][$keyIndex]);
        unset($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'][$keyIndex]);

        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys'] = array_values($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['keys']);
        $this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values'] = array_values($this->arrays[$arrayIndex]['subarrays'][$subArrayIndex]['values']);
        $this->updateTransformedJson();
    }

    public function save()
    {
        $this->validate();

        // استخدام البيانات المحولة للحفظ
        $data = $this->transformedJson;
        $result = $this->CollectionService->updateCollection($this->collection->id, [
            'name' => $this->collectionName,
            'data' => $data,
            'authorization' => $this->authorization,
            'password' => $this->authpassword,
        ]);
        if ($result->Status) {
            $this->alert('success', __('Collection updated successfully'), [
                'position' => 'center',
                'timer' => 3000,
                'toast' => true,
            ]);

            return redirect()->route('collections.index');
        } else {
            $this->alert('error', __('Failed to update collection'), [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
            ]);
        }
    }

    public function render()
    {
        return view('pages.collections.update')->layout('layouts.app');
    }
}
