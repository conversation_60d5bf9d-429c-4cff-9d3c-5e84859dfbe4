<?php

namespace App\Http\Controllers\WEB\Collections;

use App\Helpers\SubscriptionAlertHelper;
use App\Http\Controllers\WEB\LivewireController;
use App\Models\Collection;
use App\Services\CollectionService;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\WithPagination;

class Index extends LivewireController
{
    use WithPagination;
    use LivewireAlert;

    public ?string $term = null;
    public string $orderBy = 'id';
    public string $sortBy = 'asc';
    public int $perPage = 10;
    public bool $trashed = false;

    protected $CollectionService;

    public function boot(CollectionService $CollectionService)
    {
        $this->CollectionService = $CollectionService;
    }
    protected $listeners = ['refreshParent' => '$refresh'];

    public function selectedItem($action, $itemId = null)
    {
        if ($action == 'create') {
            // التحقق من حدود الميزة
            $user = auth()->user();
            $alertData = SubscriptionAlertHelper::checkFeatureAccess($user, 'create-collection');
            if ($alertData) {
                $this->alert($alertData['type'], $alertData['message']);

                return;
            }

            $this->dispatch('showCreateModel');

            return redirect()->route('collections.create');
        } elseif ($action == 'update') {
            return redirect()->route('collections.update', $itemId);
        } elseif ($action == 'show') {
            $this->dispatch('showItemModel', $itemId);
        } elseif ($action == 'delete') {
            $this->dispatch('showDeleteModel', $itemId);
        } elseif ($action == 'restore') {
            $this->dispatch('showRestoreModel', $itemId);
        } elseif ($action == 'forceDelete') {
            $this->dispatch('showForceDeleteModel', $itemId);
        }
    }

    public function ChangeStatus($itemId)
    {
        $collection = Collection::find($itemId);
        $data = [
            'status' => $collection->status == false ? true : false,
        ];
        $collection->update($data);
        $this->alert('success', __('Status updated successfully'), ['position' => 'center']);
    }

    public function getItem()
    {
        if (!empty($this->term)) {
            return $this->CollectionService->searchCollections($this->term, $this->perPage)->data;
        } else {
            return $this->CollectionService->allCollections($this->perPage, $this->trashed, $this->orderBy, $this->sortBy)->data;
        }
    }

    public function render()
    {
        return view('pages.collections.index', [
            'collections' => $this->getItem(),
        ])->layout('layouts.app');
    }
}
