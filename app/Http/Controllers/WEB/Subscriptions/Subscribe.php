<?php

namespace App\Http\Controllers\WEB\Subscriptions;

use App\Helpers\StatusCodes;
use App\Http\Controllers\WEB\LivewireController;
use App\Models\Plan;
use App\Services\SubscriptionService;
use App\Services\PaymentService;
use Illuminate\Support\Facades\Auth;

class Subscribe extends LivewireController
{
    public $plan_id;
    public $plan;
    public $user;
    public $isSubscribing = false;
    public $successMessage = '';
    public $errorMessage = '';
    public $availableGateways = [];

    public function mount($planId = null)
    {
        $this->user = Auth::user();

        if ($planId) {
            $this->plan_id = $planId;
            $this->plan = Plan::find($planId);
            
            if (!$this->plan) {
                $this->errorMessage = __('Plan not found');
                return;
            }
            
            // Check if plan is active
            if (!$this->plan->is_active) {
                $this->errorMessage = __('This plan is not available');
                return;
            }
        }
        
        // Get available payment gateways
        $this->availableGateways = $this->getAvailableGateways();
        \Log::info('Available Gateways', ['gateways' => $this->availableGateways]);
    }
    
    private function getAvailableGateways()
    {
        $gateways = [];
        
        // Check PayPal
        $paypalEnabled = \App\Helpers\SettingsHelper::getSetting('payment_paypal_enabled', false);
        $paypalClientId = \App\Helpers\SettingsHelper::getSetting('paypal_client_id');
        $paypalSecret = \App\Helpers\SettingsHelper::getSetting('paypal_client_secret');
        
        \Log::info('PayPal Check', [
            'enabled' => $paypalEnabled,
            'has_client_id' => !empty($paypalClientId),
            'has_secret' => !empty($paypalSecret)
        ]);
        
        if ($paypalEnabled && $paypalClientId && $paypalSecret) {
            $gateways['paypal'] = [
                'name' => 'PayPal',
                'icon' => 'fab fa-paypal',
                'enabled' => true
            ];
        }
        
        // Check Stripe
        $stripeEnabled = \App\Helpers\SettingsHelper::getSetting('payment_stripe_enabled', false);
        $stripePublicKey = \App\Helpers\SettingsHelper::getSetting('stripe_public_key');
        $stripeSecretKey = \App\Helpers\SettingsHelper::getSetting('stripe_secret_key');
        
        \Log::info('Stripe Check', [
            'enabled' => $stripeEnabled,
            'has_public_key' => !empty($stripePublicKey),
            'has_secret_key' => !empty($stripeSecretKey)
        ]);
        
        if ($stripeEnabled && $stripePublicKey && $stripeSecretKey) {
            $gateways['stripe'] = [
                'name' => 'Stripe',
                'icon' => 'fab fa-stripe',
                'enabled' => true
            ];
        }
        
        // Check Paymob
        $paymobEnabled = \App\Helpers\SettingsHelper::getSetting('payment_paymob_enabled', false);
        $paymobApiKey = \App\Helpers\SettingsHelper::getSetting('paymob_api_key');
        $paymobIntegrationId = \App\Helpers\SettingsHelper::getSetting('paymob_integration_id');
        
        \Log::info('Paymob Check', [
            'enabled' => $paymobEnabled,
            'has_api_key' => !empty($paymobApiKey),
            'has_integration_id' => !empty($paymobIntegrationId)
        ]);
        
        if ($paymobEnabled && $paymobApiKey && $paymobIntegrationId) {
            $gateways['paymob'] = [
                'name' => 'Paymob',
                'icon' => 'fas fa-credit-card',
                'enabled' => true
            ];
        }
        
        return $gateways;
    }

    protected function rules()
    {
        return [
            'plan_id' => 'required|exists:plans,id',
        ];
    }

    public function subscribe()
    {
        $this->isSubscribing = true;
        $this->successMessage = '';
        $this->errorMessage = '';

        $this->validate();

        try {
            $this->processFreeSubscription();
        } catch (\Exception $e) {
            $this->errorMessage = __('An error occurred').': '.$e->getMessage();
        }

        $this->isSubscribing = false;
    }
    
    public function selectGateway($gateway)
    {
        $this->isSubscribing = true;
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            // Validate gateway exists in available gateways
            if (!isset($this->availableGateways[$gateway])) {
                $this->errorMessage = __('Invalid payment gateway selected');
                $this->isSubscribing = false;
                return;
            }
            
            $this->processPaidSubscription($gateway);
        } catch (\Exception $e) {
            \Log::error('Gateway Selection Error: ' . $e->getMessage(), [
                'gateway' => $gateway,
                'user_id' => $this->user->id,
                'plan_id' => $this->plan_id
            ]);
            $this->errorMessage = __('An error occurred').': '.$e->getMessage();
        }

        $this->isSubscribing = false;
    }

    private function processFreeSubscription()
    {
        $subscriptionService = new SubscriptionService();
        $result = $subscriptionService->subscribeUserToPlan(
            $this->user,
            $this->plan_id,
            [
                'starts_at' => now(),
                'auto_renew' => true,
            ]
        );

        if ($result->Status) {
            $this->user->recordFeatureUsage('create-link-tree');
            $messages = [__('Subscription created successfully')];
            
            if (isset($result->data->linkTree)) {
                $messages[] = __('LinkTree profile has been created for you');
            }
            
            if (isset($result->data->profile)) {
                $messages[] = __('Professional profile has been created for you');
            }
            
            $this->successMessage = implode('. ', $messages);
            $this->dispatch('redirectToDashboard');
        } else {
            $this->errorMessage = $result->Code === StatusCodes::ALREADY_EXISTS 
                ? __('You already have an active subscription to this plan')
                : __('Failed to create subscription').': '.$result->data;
        }
    }

    private function processPaidSubscription($gateway = 'paypal')
    {
        try {
            $paymentService = new PaymentService();
            $result = $paymentService->processPayment(
                $this->plan->price,
                $this->user,
                $this->plan_id,
                $gateway
            );

            if ($result->Status) {
                session(['pending_plan_id' => $this->plan_id]);
                $redirectUrl = null;
                if (is_array($result->data) && isset($result->data['redirect_url'])) {
                    $redirectUrl = $result->data['redirect_url'];
                }
                
                \Log::info('Payment Result Data', ['data' => $result->data]);
                
                if ($redirectUrl) {
                    return redirect($redirectUrl);
                } else {
                    $this->errorMessage = __('Payment redirect URL not found');
                }
            } else {
                $this->errorMessage = is_string($result->data) ? $result->data : __('Payment processing failed');
            }
        } catch (\Exception $e) {
            \Log::error('Payment Processing Error: ' . $e->getMessage(), [
                'gateway' => $gateway,
                'user_id' => $this->user->id,
                'plan_id' => $this->plan_id,
                'amount' => $this->plan->price
            ]);
            $this->errorMessage = __('An error occurred').': '.$e->getMessage();
        }
    }

    public function completePaidSubscription($paymentData)
    {
        $planId = session('pending_plan_id');
        if (!$planId) {
            return false;
        }

        $subscriptionService = new SubscriptionService();
        $result = $subscriptionService->subscribeUserToPlan(
            $this->user,
            $planId,
            [
                'starts_at' => now(),
                'auto_renew' => true,
                'payment_data' => $paymentData
            ]
        );

        if ($result->Status) {
            session()->forget('pending_plan_id');
            return true;
        }

        return false;
    }

    public function handlePaymentSuccess(\Illuminate\Http\Request $request)
    {
        \Log::info('Payment Success Handler Called', [
            'request_data' => $request->all(),
            'session_data' => session()->all()
        ]);
        
        $token = $request->get('token');
        $payerId = $request->get('PayerID');
        
        if (!$token || !$payerId) {
            return redirect()->route('dashboard')->with('error', __('Invalid payment data'));
        }
        
        try {
            $paymentService = new PaymentService();
            $result = $paymentService->verifyPayment($request);
            
            if ($result->Status) {
                // Complete subscription
                $planId = session('pending_plan_id');
                if ($planId) {
                    $user = auth()->user();
                    
                    // Cancel existing subscription if any
                    $existingSubscription = $user->activeSubscription;
                    if ($existingSubscription) {
                        $existingSubscription->update([
                            'status' => 'cancelled',
                            'ends_at' => now()
                        ]);
                    }
                    
                    $subscriptionService = new SubscriptionService();
                    $subscriptionResult = $subscriptionService->subscribeUserToPlan(
                        $user,
                        $planId,
                        [
                            'starts_at' => now(),
                            'auto_renew' => true,
                            'payment_data' => $result->data,
                            'payment_id' => $result->data['payment_id'] ?? $token
                        ]
                    );
                    
                    if ($subscriptionResult->Status) {
                        // ربط الدفع بالاشتراك
                        $paymentId = $result->data['payment_id'] ?? $token;
                        \App\Models\Payment::where('payment_id', $paymentId)->update([
                            'subscription_id' => $subscriptionResult->data->id
                        ]);
                        \Log::info('Payment linked to subscription', [
                            'payment_id' => $paymentId,
                            'subscription_id' => $subscriptionResult->data->id
                        ]);
                        
                        session()->forget('pending_plan_id');
                        
                        // تحديث الجلسة لإظهار الميزات الجديدة
                        \App\Helpers\SubscriptionHelper::storeAvailableFeaturesInSession($user);
                        
                        // Get plan name and build success messages
                        $plan = Plan::find($planId);
                        $planName = $plan ? $plan->getTranslation('name', app()->getLocale()) : __('Plan');
                        
                        $messages = [__('You have successfully subscribed to :plan', ['plan' => $planName])];
                        
                        if (isset($subscriptionResult->data->linkTree)) {
                            $messages[] = __('LinkTree profile has been created for you');
                        }
                        
                        if (isset($subscriptionResult->data->profile)) {
                            $messages[] = __('Professional profile has been created for you');
                        }
                        
                        return redirect()->route('dashboard')
                            ->with('success', __('Successfully subscribed to :plan', ['plan' => $planName]))
                            ->with('livewire_alert', [
                                'type' => 'success',
                                'title' => __('Subscription Successful!'),
                                'message' => implode('. ', $messages)
                            ]);
                    }
                }
            }
            
            return redirect()->route('dashboard')
                ->with('error', __('Payment verification failed'))
                ->with('livewire_alert', [
                    'type' => 'error',
                    'title' => __('Payment Failed'),
                    'message' => __('Payment verification failed')
                ]);
            
        } catch (\Exception $e) {
            \Log::error('Payment Success Handler Error: ' . $e->getMessage());
            return redirect()->route('dashboard')
                ->with('error', __('An error occurred while processing payment'))
                ->with('livewire_alert', [
                    'type' => 'error',
                    'title' => __('Payment Error'),
                    'message' => __('An error occurred while processing payment')
                ]);
        }
    }
    
    public function handlePaymentCancel(\Illuminate\Http\Request $request)
    {
        session()->forget('pending_plan_id');
        return redirect()->route('dashboard')
            ->with('error', __('Payment was cancelled'))
            ->with('livewire_alert', [
                'type' => 'warning',
                'title' => __('Payment Cancelled'),
                'message' => __('Payment was cancelled by user')
            ]);
    }

    public function redirectToDashboard()
    {
        return redirect()->route('dashboard');
    }

    public function render()
    {
        return view('pages.subscriptions.subscribe')->layout('layouts.app');
    }
}
