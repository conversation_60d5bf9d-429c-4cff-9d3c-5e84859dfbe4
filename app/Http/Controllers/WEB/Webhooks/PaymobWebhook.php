<?php

namespace App\Http\Controllers\WEB\Webhooks;

use App\Http\Controllers\WEB\LivewireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymobWebhook extends LivewireController
{
    public function handle(Request $request)
    {
        $payload = $request->getContent();
        $data = json_decode($payload, true);
        
        if (!$data) {
            return response()->json(['error' => 'Invalid payload'], 400);
        }

        Log::info('Paymob Webhook received', ['data' => $data]);

        $eventType = $data['type'] ?? null;
        
        switch ($eventType) {
            case 'TRANSACTION':
                return $this->handleTransaction($data);
            case 'DELIVERY_STATUS':
                return $this->handleDeliveryStatus($data);
        }

        return response()->json(['status' => 'success']);
    }

    private function handleTransaction($data)
    {
        $transaction = $data['obj'] ?? [];
        $transactionId = $transaction['id'] ?? null;
        $success = $transaction['success'] ?? false;
        $amount = $transaction['amount_cents'] ?? null;
        
        if ($success) {
            Log::info('Paymob transaction succeeded', [
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return response()->json(['status' => 'transaction_succeeded']);
        } else {
            Log::info('Paymob transaction failed', [
                'transaction_id' => $transactionId
            ]);
            
            return response()->json(['status' => 'transaction_failed']);
        }
    }

    private function handleDeliveryStatus($data)
    {
        $delivery = $data['obj'] ?? [];
        $orderId = $delivery['order']['id'] ?? null;
        $status = $delivery['status'] ?? null;
        
        Log::info('Paymob delivery status updated', [
            'order_id' => $orderId,
            'status' => $status
        ]);
        
        return response()->json(['status' => 'delivery_status_updated']);
    }
}