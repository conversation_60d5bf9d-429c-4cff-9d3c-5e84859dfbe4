<?php

namespace App\Http\Controllers\WEB\Webhooks;

use App\Http\Controllers\WEB\LivewireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StripeWebhook extends LivewireController
{
    public function handle(Request $request)
    {
        $payload = $request->getContent();
        $data = json_decode($payload, true);
        
        if (!$data) {
            return response()->json(['error' => 'Invalid payload'], 400);
        }

        Log::info('Stripe Webhook received', ['data' => $data]);

        $eventType = $data['type'] ?? null;
        
        switch ($eventType) {
            case 'payment_intent.succeeded':
                return $this->handlePaymentSucceeded($data);
            case 'payment_intent.payment_failed':
                return $this->handlePaymentFailed($data);
            case 'customer.subscription.created':
                return $this->handleSubscriptionCreated($data);
            case 'customer.subscription.deleted':
                return $this->handleSubscriptionDeleted($data);
            case 'invoice.payment_succeeded':
                return $this->handleInvoicePaymentSucceeded($data);
        }

        return response()->json(['status' => 'success']);
    }

    private function handlePaymentSucceeded($data)
    {
        $paymentIntent = $data['data']['object'] ?? [];
        $paymentId = $paymentIntent['id'] ?? null;
        $amount = $paymentIntent['amount'] ?? null;
        
        Log::info('Stripe payment succeeded', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);
        
        return response()->json(['status' => 'payment_succeeded']);
    }

    private function handlePaymentFailed($data)
    {
        $paymentIntent = $data['data']['object'] ?? [];
        $paymentId = $paymentIntent['id'] ?? null;
        
        Log::info('Stripe payment failed', ['payment_id' => $paymentId]);
        
        return response()->json(['status' => 'payment_failed']);
    }

    private function handleSubscriptionCreated($data)
    {
        $subscription = $data['data']['object'] ?? [];
        $subscriptionId = $subscription['id'] ?? null;
        
        Log::info('Stripe subscription created', ['subscription_id' => $subscriptionId]);
        
        return response()->json(['status' => 'subscription_created']);
    }

    private function handleSubscriptionDeleted($data)
    {
        $subscription = $data['data']['object'] ?? [];
        $subscriptionId = $subscription['id'] ?? null;
        
        Log::info('Stripe subscription deleted', ['subscription_id' => $subscriptionId]);
        
        return response()->json(['status' => 'subscription_deleted']);
    }

    private function handleInvoicePaymentSucceeded($data)
    {
        $invoice = $data['data']['object'] ?? [];
        $invoiceId = $invoice['id'] ?? null;
        
        Log::info('Stripe invoice payment succeeded', ['invoice_id' => $invoiceId]);
        
        return response()->json(['status' => 'invoice_payment_succeeded']);
    }
}