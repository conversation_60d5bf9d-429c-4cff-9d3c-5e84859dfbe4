<?php

namespace App\Http\Controllers\WEB\Webhooks;

use App\Http\Controllers\WEB\LivewireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayPalWebhook extends LivewireController
{
    public function handle(Request $request)
    {
        $payload = $request->getContent();
        $data = json_decode($payload, true);
        
        if (!$data) {
            return response()->json(['error' => 'Invalid payload'], 400);
        }

        Log::info('PayPal Webhook received', ['data' => $data]);

        $eventType = $data['event_type'] ?? null;
        
        switch ($eventType) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                return $this->handlePaymentCompleted($data);
            case 'CHECKOUT.ORDER.APPROVED':
                return $this->handleOrderApproved($data);
            case 'BILLING.SUBSCRIPTION.CREATED':
                return $this->handleSubscriptionCreated($data);
            case 'BILLING.SUBSCRIPTION.CANCELLED':
                return $this->handleSubscriptionCancelled($data);
        }

        return response()->json(['status' => 'success']);
    }

    private function handlePaymentCompleted($data)
    {
        $resource = $data['resource'] ?? [];
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;
        
        Log::info('PayPal payment completed', ['order_id' => $orderId]);
        
        return response()->json(['status' => 'payment_completed']);
    }

    private function handleOrderApproved($data)
    {
        $resource = $data['resource'] ?? [];
        $orderId = $resource['id'] ?? null;
        
        Log::info('PayPal order approved', ['order_id' => $orderId]);
        
        return response()->json(['status' => 'order_approved']);
    }

    private function handleSubscriptionCreated($data)
    {
        $resource = $data['resource'] ?? [];
        $subscriptionId = $resource['id'] ?? null;
        
        Log::info('PayPal subscription created', ['subscription_id' => $subscriptionId]);
        
        return response()->json(['status' => 'subscription_created']);
    }

    private function handleSubscriptionCancelled($data)
    {
        $resource = $data['resource'] ?? [];
        $subscriptionId = $resource['id'] ?? null;
        
        Log::info('PayPal subscription cancelled', ['subscription_id' => $subscriptionId]);
        
        return response()->json(['status' => 'subscription_cancelled']);
    }
}