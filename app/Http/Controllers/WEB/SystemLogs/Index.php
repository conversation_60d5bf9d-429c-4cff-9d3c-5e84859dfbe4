<?php

namespace App\Http\Controllers\WEB\SystemLogs;

use App\Http\Controllers\WEB\LivewireController;
use App\Models\User;
use App\Services\SystemLogService;

class Index extends LivewireController
{

    public $level = '';
    public $action = '';
    public $module = '';
    public $user_id = '';
    public $search = '';

    protected $queryString = ['level', 'action', 'module', 'user_id', 'search'];

    public function render()
    {
        $filters = [
            'level' => $this->level,
            'action' => $this->action,
            'module' => $this->module,
            'user_id' => $this->user_id,
            'search' => $this->search,
        ];
        
        $logs = app(SystemLogService::class)->getFilteredLogs($filters, 50);
        $users = User::select('id', 'name')->orderBy('name')->get();

        return view('pages.system-logs.index', compact('logs', 'users'));
    }

    public function clearFilters()
    {
        $this->reset(['level', 'action', 'module', 'user_id', 'search']);
        $this->resetPage();
    }

    public function updatedLevel()
    {
        $this->resetPage();
    }

    public function updatedAction()
    {
        $this->resetPage();
    }

    public function updatedModule()
    {
        $this->resetPage();
    }

    public function updatedUserId()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }
}