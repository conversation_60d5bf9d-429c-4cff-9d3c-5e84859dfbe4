<?php

namespace App\Http\Controllers\WEB\Payments;

use App\Http\Controllers\WEB\LivewireController;
use App\Services\PaymentHistoryService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class PaymentHistory extends LivewireController
{
    public $stats = [];
    public $filters = [
        'status' => '',
        'gateway' => '',
        'type' => '',
        'date_from' => '',
        'date_to' => '',
    ];
    public int $perPage = 15;
    public $selectedPayment;
    public $showDetails = false;

    protected $paymentHistoryService;

    public function boot()
    {
        $this->paymentHistoryService = app(PaymentHistoryService::class);
    }

    public function mount()
    {
        // إذا كان المستخدم أدمن، توجيهه إلى صفحة الأدمن
        if (auth()->user()->role_id === 1) {
            return redirect()->route('admin.payments.index');
        }
        
        $this->loadStats();
    }

    public function getPaymentsProperty()
    {
        $service = app(PaymentHistoryService::class);
        $result = $service->getUserPayments(
            Auth::id(),
            array_merge($this->filters, ['per_page' => $this->perPage])
        );

        if ($result->Status) {
            return $result->data;
        }

        return new LengthAwarePaginator(
            collect([]), 0, $this->perPage, 1, ['path' => request()->url()]
        );
    }

    public function loadStats()
    {
        $service = app(PaymentHistoryService::class);
        $result = $service->getPaymentStats(Auth::id());

        if ($result->Status) {
            $this->stats = $result->data;
        }
    }

    public function applyFilters()
    {
        // Filters will be applied automatically via computed property
    }

    public function resetFilters()
    {
        $this->filters = [
            'status' => '',
            'gateway' => '',
            'type' => '',
            'date_from' => '',
            'date_to' => '',
        ];
    }

    public function showPaymentDetails($paymentId)
    {
        $service = app(PaymentHistoryService::class);
        $result = $service->getPaymentDetails($paymentId, Auth::id());

        if ($result->Status) {
            $this->selectedPayment = $result->data;
            $this->showDetails = true;
        }
    }

    public function closeDetails()
    {
        $this->showDetails = false;
        $this->selectedPayment = null;
    }

    public function render()
    {
        return view('pages.payments.payment-history')->layout('layouts.app');
    }
}
