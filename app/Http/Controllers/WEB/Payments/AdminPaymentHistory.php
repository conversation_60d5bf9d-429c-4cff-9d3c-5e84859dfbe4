<?php

namespace App\Http\Controllers\WEB\Payments;

use App\Http\Controllers\WEB\LivewireController;
use App\Models\User;
use App\Services\PaymentHistoryService;
use Illuminate\Pagination\LengthAwarePaginator;

class AdminPaymentHistory extends LivewireController
{
    public $filters = [
        'status' => '',
        'gateway' => '',
        'user_id' => '',
        'search' => '',
        'date_from' => '',
        'date_to' => '',
    ];
    public int $perPage = 15;
    public $selectedPayment;
    public $showDetails = false;
    public $users = [];
    public $stats = [];

    protected $paymentHistoryService;

    public function boot()
    {
        $this->paymentHistoryService = new PaymentHistoryService();
    }

    public function mount()
    {
        // التحقق من صلاحيات الأدمن
        if (auth()->user()->role_id !== 1) {
            abort(403);
        }

        $this->loadUsers();
        $this->loadStats();
    }

    public function getPaymentsProperty()
    {
        $result = $this->paymentHistoryService->getAllPayments(
            array_merge($this->filters, ['per_page' => $this->perPage])
        );

        \Log::info('Admin Payments Query Result', [
            'status' => $result->Status,
            'data_count' => $result->Status && $result->data ? $result->data->count() : 0,
            'filters' => $this->filters,
        ]);

        if ($result->Status) {
            return $result->data;
        }

        return new LengthAwarePaginator(
            collect([]), 0, $this->perPage, 1, ['path' => request()->url()]
        );
    }

    public function loadUsers()
    {
        $this->users = User::select('id', 'name', 'email')
            ->whereHas('payments')
            ->orderBy('name')
            ->get();
    }

    public function loadStats()
    {
        $result = $this->paymentHistoryService->getAdminStats();

        if ($result->Status) {
            $this->stats = $result->data;
        }
    }

    public function applyFilters()
    {
        // Filters will be applied automatically via computed property
    }

    public function resetFilters()
    {
        $this->filters = [
            'status' => '',
            'gateway' => '',
            'user_id' => '',
            'search' => '',
            'date_from' => '',
            'date_to' => '',
        ];
    }

    public function showPaymentDetails($paymentId)
    {
        $result = $this->paymentHistoryService->getPaymentDetails($paymentId);

        if ($result->Status) {
            $this->selectedPayment = $result->data;
            $this->showDetails = true;
        }
    }

    public function closeDetails()
    {
        $this->showDetails = false;
        $this->selectedPayment = null;
    }

    public function render()
    {
        return view('pages.admin.payments.admin-payment-history')->layout('layouts.app');
    }
}
