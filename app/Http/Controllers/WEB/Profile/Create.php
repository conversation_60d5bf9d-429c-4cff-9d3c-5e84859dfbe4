<?php

namespace App\Http\Controllers\WEB\Profile;

use App\Services\ProfileService;
use Jan<PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use LivewireAlert, WithFileUploads;

    protected $ProfileService;
    
    public $name = [];
    public $slug;
    public $bio = [];
    public $job_title = [];
    public $email;
    public $phone;
    public $website;
    public $domain;
    public $address = [];
    public $theme_id;
    public $settings = [];
    public $seo_data = [];
    public $visibility = 'public';
    public $is_active = true;
    public $avatar;
    public $cover;
    
    // للتحقق من الـ slug
    public $isSlugAvailable = false;
    public $slugMessage = '';
    public $slugClass = '';

    public function boot(ProfileService $ProfileService)
    {
        $this->ProfileService = $ProfileService;
    }

    public function updatedSlug()
    {
        $this->checkSlug();
    }

    private function checkSlug()
    {
        if (empty($this->slug)) {
            $this->slugMessage = __('slug_required');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        if (strlen($this->slug) < 4) {
            $this->slugMessage = __('slug_min_length');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        if (!preg_match('/^[a-zA-Z0-9-]+$/', $this->slug)) {
            $this->slugMessage = __('slug_invalid_characters');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        $exists = \App\Models\Profile::where('slug', $this->slug)->exists();
        
        $this->isSlugAvailable = !$exists;
        $this->slugMessage = $exists ? __('slug_unavailable') : __('slug_available');
        $this->slugClass = $exists ? 'text-red-500' : 'text-green-500';
    }

    protected function rules()
    {
        return [
            'name' => 'required|array',
            'slug' => 'required|string|unique:profiles,slug|regex:/^[a-z0-9-]+$/',
            'bio' => 'nullable|array',
            'job_title' => 'nullable|array',
            'email' => 'nullable|email',
            'phone' => 'nullable|string',
            'website' => 'nullable|url',
            'domain' => 'nullable|string|unique:profiles,domain',
            'address' => 'nullable|array',
            'theme_id' => 'nullable|exists:themes,id',
            'visibility' => 'required|in:public,private,unlisted',
            'is_active' => 'required|boolean',
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => __('The profile name field is required.'),
            'slug.required' => __('The slug field is required.'),
            'slug.unique' => __('The slug has already been taken.'),
            'email.email' => __('Please enter a valid email address.'),
            'website.url' => __('Please enter a valid website URL.'),
        ];
    }

    public function create()
    {
        if (!$this->isSlugAvailable) {
            $this->alert('error', __('Please fix the errors before saving'));
            return;
        }

        try {
            $this->validate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->alert('error', __('Validation failed'));
            throw $e;
        }

        $data = [
            'user_id' => auth()->user()->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'bio' => $this->bio,
            'job_title' => $this->job_title,
            'email' => $this->email,
            'phone' => $this->phone,
            'website' => $this->website,
            'domain' => $this->domain,
            'address' => $this->address,
            'theme_id' => $this->theme_id,
            'settings' => $this->settings,
            'seo_data' => $this->seo_data,
            'visibility' => $this->visibility,
            'is_active' => $this->is_active,
        ];

        $result = $this->ProfileService->createProfile($data);
        
        if ($result->Status) {
            $user = auth()->user();
            $user->recordFeatureUsage('create-profile');
            
            if ($this->avatar) {
                $result->data->addMedia($this->avatar)->toMediaCollection('avatar');
            }
            if ($this->cover) {
                $result->data->addMedia($this->cover)->toMediaCollection('cover');
            }
            
            $this->alert('success', __('Profile created successfully'));
            return redirect()->route('profiles.index');
        } else {
            $this->alert('error', __('Failed to create profile'));
        }
    }

    public function cancel()
    {
        return redirect()->route('profiles.index');
    }

    public function render()
    {
        return view('pages.profile.create')->layout('layouts.app');
    }
}
