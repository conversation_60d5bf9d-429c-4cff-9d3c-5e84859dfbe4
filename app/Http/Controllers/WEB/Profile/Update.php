<?php

namespace App\Http\Controllers\WEB\Profile;

use App\Models\Profile;
use App\Services\ProfileService;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class Update extends Component
{
    use LivewireAlert, WithFileUploads;

    protected $ProfileService;
    public $profile;
    
    public $name = [];
    public $slug;
    public $bio = [];
    public $job_title = [];
    public $email;
    public $phone;
    public $website;
    public $domain;
    public $address = [];
    public $theme_id;
    public $settings = [];
    public $seo_data = [];
    public $visibility = 'public';
    public $is_active = true;
    public $avatar;
    public $cover;

    public function boot(ProfileService $ProfileService)
    {
        $this->ProfileService = $ProfileService;
    }

    public function mount(Profile $profile)
    {
        $this->profile = $profile;
        
        \Log::info('=== Profile Mount Start ===');
        \Log::info('Original name: ' . json_encode($this->profile->name));
        \Log::info('Original address: ' . json_encode($this->profile->address));
        
        // Use getTranslations() method to get proper translation arrays
        $this->name = $this->profile->getTranslations('name') ?: ['ar' => $this->profile->name ?? '', 'en' => ''];
        $this->bio = $this->profile->getTranslations('bio') ?: ['ar' => $this->profile->bio ?? '', 'en' => ''];
        $this->job_title = $this->profile->getTranslations('job_title') ?: ['ar' => $this->profile->job_title ?? '', 'en' => ''];
        $this->address = $this->profile->getTranslations('address') ?: ['ar' => $this->profile->address ?? '', 'en' => ''];
        
        \Log::info('Converted name: ' . json_encode($this->name));
        \Log::info('Converted address: ' . json_encode($this->address));
        
        $this->slug = $this->profile->slug;
        $this->email = $this->profile->email;
        $this->phone = $this->profile->phone;
        $this->website = $this->profile->website;
        $this->domain = $this->profile->domain;
        $this->theme_id = $this->profile->theme_id;
        $this->settings = $this->profile->settings ?? [];
        $this->seo_data = $this->profile->seo_data ?? [];
        $this->visibility = $this->profile->visibility;
        $this->is_active = $this->profile->is_active;
        
        \Log::info('=== Profile Mount End ===');
    }

    protected function rules()
    {
        $rules = [
            'name' => 'required|array',
            'slug' => 'required|string|unique:profiles,slug,'.$this->profile->id.'|regex:/^[a-z0-9-]+$/',
            'bio' => 'nullable|array',
            'job_title' => 'nullable|array',
            'email' => 'nullable|email',
            'phone' => 'nullable|string',
            'website' => 'nullable|url',
            'domain' => 'nullable|string|unique:profiles,domain,'.$this->profile->id,
            'address' => 'nullable|array',
            'theme_id' => 'nullable|exists:themes,id',
            'visibility' => 'required|in:public,private,unlisted',
            'is_active' => 'required|boolean',
        ];
        
        \Log::info('Validation rules: ' . json_encode($rules));
        return $rules;
    }

    protected function messages()
    {
        return [
            'name.required' => __('The profile name field is required.'),
            'slug.required' => __('The slug field is required.'),
            'slug.unique' => __('The slug has already been taken.'),
            'email.email' => __('Please enter a valid email address.'),
            'website.url' => __('Please enter a valid website URL.'),
        ];
    }

    public function save()
    {
        \Log::info('=== Profile Update Validation Start ===');
        
        // Convert strings to arrays if needed (fallback)
        if (!is_array($this->name)) {
            $this->name = ['ar' => $this->name ?? '', 'en' => ''];
        }
        if (!is_array($this->address)) {
            $this->address = ['ar' => $this->address ?? '', 'en' => ''];
        }
        if (!is_array($this->bio)) {
            $this->bio = ['ar' => $this->bio ?? '', 'en' => ''];
        }
        if (!is_array($this->job_title)) {
            $this->job_title = ['ar' => $this->job_title ?? '', 'en' => ''];
        }
        
        \Log::info('Name data: ' . json_encode($this->name));
        \Log::info('Bio data: ' . json_encode($this->bio));
        \Log::info('Job title data: ' . json_encode($this->job_title));
        \Log::info('Address data: ' . json_encode($this->address));
        \Log::info('Slug: ' . $this->slug);
        \Log::info('Email: ' . $this->email);
        
        try {
            $this->validate();
            \Log::info('Validation passed successfully');
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation failed with errors: ' . json_encode($e->errors()));
            \Log::error('Validation exception message: ' . $e->getMessage());
            $this->alert('error', __('Validation failed') . ': ' . json_encode($e->errors()));
            throw $e;
        }

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'bio' => $this->bio,
            'job_title' => $this->job_title,
            'email' => $this->email,
            'phone' => $this->phone,
            'website' => $this->website,
            'domain' => $this->domain,
            'address' => $this->address,
            'theme_id' => $this->theme_id,
            'settings' => $this->settings,
            'seo_data' => $this->seo_data,
            'visibility' => $this->visibility,
            'is_active' => $this->is_active,
        ];

        $result = $this->ProfileService->updateProfile($this->profile->id, $data);
        
        if ($result->Status) {
            if ($this->avatar) {
                $this->profile->addMedia($this->avatar)->toMediaCollection('avatar');
            }
            if ($this->cover) {
                $this->profile->addMedia($this->cover)->toMediaCollection('cover');
            }
            
            $this->alert('success', __('Profile updated successfully'));
            return redirect()->route('profiles.index');
        } else {
            $this->alert('error', __('Failed to update profile'));
        }
    }

    public function cancel()
    {
        return redirect()->route('profiles.index');
    }

    public function render()
    {
        return view('pages.profile.update')->layout('layouts.app');
    }
}
