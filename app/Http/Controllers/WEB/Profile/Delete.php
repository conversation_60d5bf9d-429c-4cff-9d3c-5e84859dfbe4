<?php

namespace App\Http\Controllers\WEB\Profile;

use App\Services\ProfileService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Delete extends Component
{
    use LivewireAlert;
    use AuthorizesRequests;

    public $showDeleteModel = false;
    public $showRestoreModel = false;
    public $showForceDeleteModel = false;
    public $itemId;
    protected $ProfileService;

    public function __construct()
    {
        $this->ProfileService = new ProfileService();
    }

    protected $listeners = ['showDeleteModel', 'showRestoreModel', 'showForceDeleteModel'];

    public function showDeleteModel($itemId)
    {
        $this->itemId = $itemId;
        $this->showDeleteModel = true;
    }

    public function closeDeleteModel()
    {
        $this->showDeleteModel = false;
        $this->reset();
    }

    public function delete()
    {
        $deleted = $this->ProfileService->deleteProfile($this->itemId);
        if ($deleted->Status) {
            $this->reset();
            $this->closeDeleteModel();
            $this->dispatch('refreshParent');
            $this->alert('success', __('Deleted successfully'), ['position' => 'center']);
        }
    }

    public function showRestoreModel($itemId)
    {
        $this->itemId = $itemId;
        $this->showRestoreModel = true;
    }

    public function closeRestoreModel()
    {
        $this->showRestoreModel = false;
        $this->reset();
    }

    public function restore()
    {
        $restore = $this->ProfileService->restoreProfile($this->itemId);
        if ($restore->Status) {
            $this->reset();
            $this->closeRestoreModel();
            $this->dispatch('refreshParent');
            $this->alert('success', __('Restored successfully'), ['position' => 'center']);
        }
    }

    public function showForceDeleteModel($itemId)
    {
        $this->itemId = $itemId;
        $this->showForceDeleteModel = true;
    }

    public function closeForceDeleteModel()
    {
        $this->showForceDeleteModel = false;
        $this->reset();
    }

    public function forceDelete()
    {
        $forceDelete = $this->ProfileService->forceDeleteProfile($this->itemId);
        if ($forceDelete->Status) {
            $this->reset();
            $this->closeForceDeleteModel();
            $this->alert('success', __('Deleted successfully'), ['position' => 'center']);
            $this->dispatch('refreshParent');
        }
    }

    public function render()
    {
        return view('pages.profile.delete');
    }
}
