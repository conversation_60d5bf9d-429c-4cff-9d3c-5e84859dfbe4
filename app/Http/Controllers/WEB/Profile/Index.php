<?php

namespace App\Http\Controllers\WEB\Profile;

use App\Http\Controllers\WEB\LivewireController;
use App\Services\ProfileService;
use App\Helpers\SubscriptionAlertHelper;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class Index extends LivewireController
{
    use LivewireAlert;
    
    protected $ProfileService;

    public function boot(ProfileService $ProfileService)
    {
        $this->authorize('viewAny', \App\Models\Profile::class);
        $this->ProfileService = $ProfileService;
    }

    protected $listeners = ['refreshParent' => '$refresh'];

    public function selectedItem($action, $itemId = null)
    {
        if ($action == 'create') {
            $user = auth()->user();
            $alertData = SubscriptionAlertHelper::checkFeatureAccess($user, 'personal-profile');
            if ($alertData) {
                $this->alert($alertData['type'], $alertData['message'], $alertData['options']);
                return;
            }
            return redirect()->route('profiles.create');
        } elseif ($action == 'update') {
            return redirect()->route('profiles.update', $itemId);
        } elseif ($action == 'show') {
            $this->dispatch('showItemModel', $itemId);
        } elseif ($action == 'delete') {
            $this->dispatch('showDeleteModel', $itemId);
        } elseif ($action == 'restore') {
            $this->dispatch('showRestoreModel', $itemId);
        } elseif ($action == 'forceDelete') {
            $this->dispatch('showForceDeleteModel', $itemId);
        }
    }

    public function getItem()
    {
        if (!empty($this->term)) {
            return $this->ProfileService->searchProfiles($this->term, $this->perPage)->data;
        } else {
            return $this->ProfileService->allProfiles($this->perPage, $this->trashed, $this->orderBy, $this->sortBy)->data;
        }
    }

    public function render()
    {
        return view('pages.profile.index', [
            'profiles' => $this->getItem(),
        ])->layout('layouts.app');
    }
}
