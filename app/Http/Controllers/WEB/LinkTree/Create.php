<?php

namespace App\Http\Controllers\WEB\LinkTree;

use App\Services\LinkTreeService;
use App\Helpers\SubscriptionHelper;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use LivewireAlert;
    use WithFileUploads;
    protected $LinkTreeService;
    public $name = [];

    public $slug;
    public $description = [];
    public $job_title = [];
    public $keywords = [];
    public $default_language;
    public $title_color;
    public $description_color;
    public $job_title_color;
    public $icon_color;
    public $button_text_color;
    public $button_color;
    public $background_color;
    public $is_active = true;
    public $avatar;
    public $cover;
    public $tempLinks = [];
    public $socialMedia = [];
    
    // SEO and Rich Snippets fields
    public $alternate_names_input = '';
    public $email = '';
    public $phone = '';
    public $birth_date = '';
    public $address_input = '';
    public $works_for_input = '';
    public $education_input = '';
    public $skills_input = '';
    public $languages_input = '';
    public $knows_about_input = '';
    public $business_info_input = '';
    public $price_range = '';
    public $enable_rich_snippets = true;
    
    // للتحقق من الـ slug 
    public $isSlugAvailable = false;
    public $slugMessage = '';
    public $slugClass = '';

    public function __construct()
    {
        $this->LinkTreeService = new LinkTreeService();
    }

    public function closePage()
    {
        return redirect()->route('linktree.index');
    }
    
    public function updatedSlug()
    {
        $this->checkSlug();
    }

    private function checkSlug()
    {
        if (empty($this->slug)) {
            $this->slugMessage = __('slug_required');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        if (strlen($this->slug) < 4) {
            $this->slugMessage = __('slug_min_length');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        if (!preg_match('/^[a-zA-Z0-9-]+$/', $this->slug)) {
            $this->slugMessage = __('slug_invalid_characters');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;
            return;
        }

        // تحقق مما إذا كان الـ slug موجود بالفعل
        $exists = \App\Models\LinkTree::where('slug', $this->slug)->exists();
        
        $this->isSlugAvailable = !$exists;
        $this->slugMessage = $exists ? __('slug_unavailable') : __('slug_available');
        $this->slugClass = $exists ? 'text-red-500' : 'text-green-500';
    }

    protected function rules()
    {
        return [
            'name' => 'required|array',
            'slug' => 'required|string|unique:link_trees,slug|regex:/^[a-z0-9-]+$/',
            'description' => 'required|array',
            'job_title' => 'required|array',
            'keywords' => 'nullable|array',
            'default_language' => 'required|exists:languages,id',
            'title_color' => 'nullable|string',
            'description_color' => 'nullable|string',
            'job_title_color' => 'nullable|string',
            'icon_color' => 'nullable|string',
            'button_text_color' => 'nullable|string',
            'button_color' => 'nullable|string',
            'background_color' => 'nullable|string',
            'is_active' => 'required|boolean',
            'tempLinks' => 'nullable|array',
            'tempLinks.*.name' => 'required|array',
            'tempLinks.*.url' => 'required|string',
            'tempLinks.*.target' => 'nullable|boolean',
            'tempLinks.*.order' => 'nullable|integer',
            'tempLinks.*.is_active' => 'nullable|boolean',
            'socialMedia' => 'nullable|array',
            'socialMedia.*.platform' => 'required|string',
            'socialMedia.*.url' => 'required|url',
            'socialMedia.*.order' => 'nullable|integer',
            'socialMedia.*.is_active' => 'nullable|boolean',
            // SEO fields validation
            'alternate_names_input' => 'nullable|string',
            'email' => 'nullable|email',
            'phone' => 'nullable|string',
            'birth_date' => 'nullable|date',
            'address_input' => 'nullable|string',
            'works_for_input' => 'nullable|string',
            'education_input' => 'nullable|string',
            'skills_input' => 'nullable|string',
            'languages_input' => 'nullable|string',
            'knows_about_input' => 'nullable|string',
            'business_info_input' => 'nullable|string',
            'price_range' => 'nullable|string',
            'enable_rich_snippets' => 'nullable|boolean',
        ];
    }

    private function addDefaultSocialMedia()
    {
        $defaultPlatforms = ['facebook', 'twitter', 'instagram', 'linkedin'];

        foreach ($defaultPlatforms as $index => $platform) {
            $this->socialMedia[] = [
                'platform' => $platform,
                'url' => 'https://'.$platform.'.com/',
                'order' => $index + 1,
                'is_active' => false,
            ];
        }
    }

    public function addNewSocialMedia()
    {
        $platformUrls = [
            'facebook' => 'https://facebook.com/',
            'twitter' => 'https://twitter.com/',
            'instagram' => 'https://instagram.com/',
            'linkedin' => 'https://linkedin.com/',
            'youtube' => 'https://youtube.com/',
            'github' => 'https://github.com/',
            'dribbble' => 'https://dribbble.com/',
            'behance' => 'https://behance.net/',
            'pinterest' => 'https://pinterest.com/',
            'snapchat' => 'https://snapchat.com/',
            'telegram' => 'https://telegram.org/',
            'whatsapp' => 'https://api.whatsapp.com/send?phone=', // WhatsApp link for messaging
            'discord' => 'https://discord.com/',
            'other' => '',
        ];

        $selectedPlatform = 'facebook'; // Replace this with the selected platform from the dropdown
        $url = $platformUrls[$selectedPlatform] ?? '';

        // If the selected platform is WhatsApp, append a phone number placeholder
        if ($selectedPlatform === 'whatsapp') {
            $url .= '1234567890'; // Replace '1234567890' with the desired phone number
        }

        $this->socialMedia[] = [
            'platform' => $selectedPlatform,
            'url' => $url,
            'order' => count($this->socialMedia) + 1,
            'is_active' => true,
        ];
    }

    // add updateSocialMediaUrl function
    public function updateSocialMediaUrl($index)
    {
        $platform = $this->socialMedia[$index]['platform'];
        $platformUrls = [
            'facebook' => 'https://facebook.com/',
            'twitter' => 'https://twitter.com/',
            'instagram' => 'https://instagram.com/',
            'linkedin' => 'https://linkedin.com/',
            'youtube' => 'https://youtube.com/',
            'github' => 'https://github.com/',
            'dribbble' => 'https://dribbble.com/',
            'behance' => 'https://behance.net/',
            'pinterest' => 'https://pinterest.com/',
            'snapchat' => 'https://snapchat.com/',
            'telegram' => 'https://telegram.org/',
            'whatsapp' => 'https://api.whatsapp.com/send?phone=', // WhatsApp link for messaging
            'discord' => 'https://discord.com/',
            'other' => '',
        ];
        $url = $platformUrls[$platform] ?? '';
        // If the selected platform is WhatsApp, append a phone number placeholder
        if ($platform === 'whatsapp') {
            $url .= '1234567890'; // Replace '1234567890' with the desired phone number
        }
        $this->socialMedia[$index]['url'] = $url;
    }

    public function addNewLink()
    {
        $this->tempLinks[] = [
            'name' => __('New Link'),
            'url' => 'https://',
            'target' => true,
            'order' => count($this->tempLinks) + 1,
            'is_active' => true,
        ];
    }

    public function removeLink($index)
    {
        if (isset($this->tempLinks[$index])) {
            array_splice($this->tempLinks, $index, 1);
        }
    }

    public function removeSocialMedia($index)
    {
        if (isset($this->socialMedia[$index])) {
            array_splice($this->socialMedia, $index, 1);
        }
    }

    public function create()
    {
        // تحقق من توفر الـ slug قبل الإنشاء
        if (!$this->isSlugAvailable) {
            $this->alert('error', __('Please fix the errors before saving'));
            return;
        }
        

        
        $data = [
            'user_id' => auth()->user()->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'description' => $this->description,
            'job_title' => $this->job_title,
            'keywords' => $this->keywords,
            'default_language' => $this->default_language,
            'title_color' => $this->title_color,
            'description_color' => $this->description_color,
            'job_title_color' => $this->job_title_color,
            'icon_color' => $this->icon_color,
            'button_text_color' => $this->button_text_color,
            'button_color' => $this->button_color,
            'background_color' => $this->background_color,
            'is_active' => $this->is_active,
            'links' => $this->tempLinks,
            'social_media' => $this->socialMedia,
            'avatar' => $this->avatar,
            'cover' => $this->cover,
            // SEO data
            'alternate_names' => $this->alternate_names_input ? explode(',', $this->alternate_names_input) : null,
            'email' => $this->email,
            'phone' => $this->phone,
            'birth_date' => $this->birth_date ?: null,
            'address' => $this->parseAddressInput($this->address_input),
            'works_for' => $this->works_for_input ? ['name' => $this->works_for_input, 'type' => 'Organization'] : null,
            'education' => $this->education_input ? explode("\n", $this->education_input) : null,
            'skills' => $this->skills_input ? explode(',', $this->skills_input) : null,
            'languages' => $this->languages_input ? explode(',', $this->languages_input) : null,
            'knows_about' => $this->knows_about_input ? explode(',', $this->knows_about_input) : null,
            'business_info' => $this->business_info_input ? ['description' => $this->business_info_input] : null,
            'price_range' => $this->price_range,
            'enable_rich_snippets' => $this->enable_rich_snippets,
        ];
        $result = $this->LinkTreeService->createLinkTree($data);
        if ($result->Status) {
            // تسجيل استخدام الميزة
            $user->recordFeatureUsage('create-link-tree');
            
            $this->alert('success', __('Data created successfully'));
            $this->dispatch('refreshParent');

            return redirect()->route('linktree.index');
        }
    }

    private function parseAddressInput($input)
    {
        if (empty($input)) return null;
        
        $lines = explode("\n", $input);
        return [
            'streetAddress' => $lines[0] ?? '',
            'addressLocality' => $lines[1] ?? '',
            'addressCountry' => $lines[2] ?? '',
            'postalCode' => $lines[3] ?? ''
        ];
    }

    public function render()
    {
        return view('pages.link-tree.create')->layout('layouts.app');
    }
}
