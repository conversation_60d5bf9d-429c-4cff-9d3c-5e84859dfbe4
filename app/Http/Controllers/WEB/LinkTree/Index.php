<?php

namespace App\Http\Controllers\WEB\LinkTree;

use App\Helpers\SubscriptionAlertHelper;
use App\Http\Controllers\WEB\LivewireController;
use App\Services\LinkTreeService;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class Index extends LivewireController
{
    use LivewireAlert;
    protected $LinkTreeService;
    public $canCreateLinkTree = false;

    public function __construct()
    {
        $this->LinkTreeService = new LinkTreeService();
    }

    public function mount()
    {
        $this->canCreateLinkTree = auth()->user()->canUseFeature('create-link-tree');
    }
    protected $listeners = ['refreshParent' => '$refresh'];

    public function updatingTerm()
    {
        parent::updatingTerm();
    }

    public function updatingOrderBy()
    {
        parent::updatingOrderBy();
    }

    public function updatingSortBy()
    {
        parent::updatingSortBy();
    }

    public function updatingPerPage()
    {
        parent::updatingPerPage();
    }

    public function updatingTrashed()
    {
        parent::updatingTrashed();
    }

    public function toggleTrashed()
    {
        parent::toggleTrashed();
    }

    public function selectedItem($action, $itemId = null)
    {
        if ($action == 'create') {
            // التحقق من حدود الميزة
            $user = auth()->user();
            $alertData = SubscriptionAlertHelper::checkFeatureAccess($user, 'create-link-tree');
            if ($alertData) {
                $this->alert($alertData['type'], $alertData['message']);

                return;
            }

            return redirect()->route('linktree.create');
        } elseif ($action == 'update') {
            return redirect()->route('linktree.update', $itemId);
        // $this->dispatch('showUpdateModel', $itemId);linktree.update
        } elseif ($action == 'show') {
            $this->dispatch('showItemModel', $itemId);
        } elseif ($action == 'delete') {
            $this->dispatch('showDeleteModel', $itemId);
        } elseif ($action == 'restore') {
            $this->dispatch('showRestoreModel', $itemId);
        } elseif ($action == 'forceDelete') {
            $this->dispatch('showForceDeleteModel', $itemId);
        }
    }

    public function getItem()
    {
        if (!empty($this->term)) {
            return $this->LinkTreeService->searchLinkTree($this->term, $this->perPage)->data;
        } else {
            return $this->LinkTreeService->allLinkTrees($this->perPage, $this->trashed, $this->orderBy, $this->sortBy)->data;
        }
    }

    public function render()
    {
        return view('pages.link-tree.index', ['linkTrees' => $this->getItem()])->layout('layouts.app');
    }
}
