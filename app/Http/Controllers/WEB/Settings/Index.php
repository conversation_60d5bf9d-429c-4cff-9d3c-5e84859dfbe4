<?php

namespace App\Http\Controllers\WEB\Settings;

use App\Models\Language;
use App\Models\Setting;
use App\Services\SettingService;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class Index extends Component
{
    use WithFileUploads;
    use LivewireAlert;
    
    public $settings = [];
    public $languages = [];
    public $activeTab = 'basic';
    public $activeLanguage = 'en';
    public $site_logo_ar;
    public $site_logo_en;
    public $site_favicon;
    public $og_image;
    
    // Rich Snippets input fields
    public $opening_hours_input = '';
    public $payment_methods_input = '';
    public $custom_schema_input = '';
    protected $listeners = ['refreshParent' => '$refresh'];
    public $readyToLoad = false;
    protected $settingService;

    public function boot(SettingService $settingService)
    {
        $this->settingService = $settingService;
    }

    public function loadItems()
    {
        $this->readyToLoad = true;
    }

    public function mount()
    {
        $this->languages = Language::where('status', 1)->get();
        $this->activeLanguage = $this->languages->first()->short ?? 'en';
        $result = $this->settingService->getSettings();
        if ($result->Status && $result->data) {
            $settingsArray = $result->data->toArray();
            
            // تحويل جميع الحقول البولين إلى boolean للعرض في checkboxes
            $booleanFields = [
                'maintenance_mode', 'user_registration', 'email_verification', 
                'two_factor_auth', 'dark_mode_enabled', 'email_notifications',
                'sms_notifications', 'push_notifications', 'cache_enabled',
                'image_optimization', 'lazy_loading', 'minify_css', 'minify_js',
                'auto_backup', 'error_logging', 'activity_logging', 'paypal_sandbox',
                'paymob_sandbox', 'enable_rich_snippets', 'payment_stripe_enabled',
                'payment_paypal_enabled', 'payment_paymob_enabled', 'payment_credit_card_enabled',
                'recaptcha_enabled'
            ];
            
            foreach ($booleanFields as $field) {
                if (isset($settingsArray[$field])) {
                    $settingsArray[$field] = (bool) $settingsArray[$field];
                }
            }
            
            $this->settings = $settingsArray;
            
            // Load Rich Snippets input fields
            $this->opening_hours_input = is_array($settingsArray['opening_hours'] ?? null) ? json_encode($settingsArray['opening_hours']) : ($settingsArray['opening_hours'] ?? '');
            $this->payment_methods_input = is_array($settingsArray['payment_methods'] ?? null) ? implode(', ', $settingsArray['payment_methods']) : ($settingsArray['payment_methods'] ?? '');
            $this->custom_schema_input = is_array($settingsArray['custom_schema_markup'] ?? null) ? json_encode($settingsArray['custom_schema_markup'], JSON_PRETTY_PRINT) : ($settingsArray['custom_schema_markup'] ?? '');
        } else {
            // قيم افتراضية إذا لم توجد إعدادات
            $this->settings = [
                'maintenance_mode' => '0',
                'user_registration' => '1',
                'email_verification' => '0',
                'two_factor_auth' => '0',
                'dark_mode_enabled' => '1',
                'default_language' => $this->languages->first()->id ?? 1,
                'timezone' => 'Asia/Dubai',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'currency_position' => 'before',
                'session_lifetime' => 120,
                'max_file_size' => 2048,
                'email_notifications' => '1',
                'sms_notifications' => '0',
                'push_notifications' => '0',
                'cache_enabled' => '1',
                'image_optimization' => '1',
                'lazy_loading' => '1',
                'minify_css' => '0',
                'minify_js' => '0',
                'auto_backup' => '0',
                'error_logging' => '1',
                'activity_logging' => '1',
                'paypal_sandbox' => '1',
                'paymob_sandbox' => '1',
                'payment_stripe_enabled' => '1',
                'payment_paypal_enabled' => '1',
                'payment_paymob_enabled' => '0',
                'payment_credit_card_enabled' => '1',
                'recaptcha_enabled' => '0',

                'payment_stripe_enabled' => '1',
                'payment_paypal_enabled' => '1',
                'payment_paymob_enabled' => '0',
                'cache_duration' => 3600,
                'log_retention_days' => 30,
                'mail_port' => 587,
                'font_family' => 'Inter',
                'default_theme' => 'light',
                'backup_frequency' => 'weekly',
                'mail_driver' => 'smtp',
                'mail_encryption' => 'tls',
                'enable_rich_snippets' => '1'
            ];
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function setActiveLanguage($language)
    {
        $this->activeLanguage = $language;
    }

    public function updateSettings()
    {
        $data = $this->settings;
        

        
        // Process Rich Snippets data
        if (!empty($this->opening_hours_input)) {
            $data['opening_hours'] = json_decode($this->opening_hours_input, true) ?: $this->opening_hours_input;
        }
        
        if (!empty($this->payment_methods_input)) {
            $data['payment_methods'] = array_map('trim', explode(',', $this->payment_methods_input));
        }
        
        if (!empty($this->custom_schema_input)) {
            $data['custom_schema_markup'] = json_decode($this->custom_schema_input, true) ?: $this->custom_schema_input;
        }
        
        // Handle media uploads
        if (!empty($this->site_logo_ar)) {
            $url = 'storage/'.$this->site_logo_ar->store('images', 'public');
            $data['site_logo_ar'] = $url;
        }
        
        if (!empty($this->site_logo_en)) {
            $url = 'storage/'.$this->site_logo_en->store('images', 'public');
            $data['site_logo_en'] = $url;
        }
        
        if (!empty($this->site_favicon)) {
            $url = 'storage/'.$this->site_favicon->store('images', 'public');
            $data['site_favicon'] = $url;
        }
        
        if (!empty($this->og_image)) {
            $url = 'storage/'.$this->og_image->store('images', 'public');
            $data['og_image'] = $url;
        }
        
        $result = $this->settingService->updateSettings($data);
        if ($result->Status) {
            $this->resetValidation();
            $this->resetErrorBag();
            $this->mount();
            $this->alert('success', __('Settings updated successfully'), ['position' => 'center']);
        } else {
            $this->alert('error', __('Failed to update settings'), ['position' => 'center']);
        }
    }

    public function render()
    {
        return view('pages.settings.index', [
            'settings' => $this->settings,
            'languages' => $this->languages
        ]);
    }
}
