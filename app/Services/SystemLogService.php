<?php

namespace App\Services;

use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class SystemLogService
{
    public static function log(array $data): SystemLog
    {
        $defaultData = [
            'user_id' => Auth::check() ? Auth::id() : null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'session_id' => session()->getId(),
            'url' => Request::fullUrl(),
            'method' => Request::method(),
        ];

        try {
            return SystemLog::create(array_merge($defaultData, $data));
        } catch (\Exception $e) {
            // إذا فشل حفظ اللوج، لا نريد كسر التطبيق
            return new SystemLog();
        }
    }

    public static function logModelAction(string $action, Model $model, array $oldValues = null, array $newValues = null, string $description = null): SystemLog
    {
        return self::log([
            'level' => 'info',
            'action' => $action,
            'model' => get_class($model),
            'model_id' => $model->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'description' => $description ?? ucfirst($action) . ' ' . class_basename($model),
            'module' => strtolower(class_basename($model)),
        ]);
    }

    public static function logAuth(string $action, string $description, string $level = 'info'): SystemLog
    {
        return self::log([
            'level' => $level,
            'action' => $action,
            'description' => $description,
            'module' => 'auth',
        ]);
    }

    public static function logError(\Throwable $exception, string $module = null): SystemLog
    {
        return self::log([
            'level' => 'error',
            'action' => 'exception',
            'description' => $exception->getMessage(),
            'module' => $module ?? 'system',
            'metadata' => [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ],
        ]);
    }

    public function getFilteredLogs(array $filters, int $perPage = 50)
    {
        return SystemLog::with('user')
            ->when($filters['level'], fn($q) => $q->where('level', $filters['level']))
            ->when($filters['action'], fn($q) => $q->where('action', 'like', '%' . $filters['action'] . '%'))
            ->when($filters['module'], fn($q) => $q->where('module', 'like', '%' . $filters['module'] . '%'))
            ->when($filters['user_id'], fn($q) => $q->where('user_id', $filters['user_id']))
            ->when($filters['search'], fn($q) => $q->where('description', 'like', '%' . $filters['search'] . '%'))
            ->latest('logged_at')
            ->paginate($perPage);
    }
}