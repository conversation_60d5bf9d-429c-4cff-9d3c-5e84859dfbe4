<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\Theme;
use App\Traits\LogsTrait;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ThemeService extends BaseService
{
    use AuthorizesRequests, LogsTrait;

    /**
     * Get all themes with pagination
     */
    public function allThemes(int $itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc', $filters = []): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $query = Theme::with(['user'])->withCount('profiles');

        if ($trashed) {
            $query->onlyTrashed();
        }

        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $query->where('is_active', true);
            } elseif ($filters['status'] === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if (!empty($filters['showOnlyDefault'])) {
            $query->where('is_default', true);
        }

        // Non-admin users can only see their own themes
        if (auth()->user()->role_id != 1) {
            $query->where('user_id', auth()->user()->id);
        }

        $themes = $query->orderBy($orderBy, $sortBy)->paginate($itemsPerPage);

        if ($themes->count() >= 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $themes;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    /**
     * Search themes
     */
    public function searchThemes(string $term, int $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $query = Theme::with(['user'])
            ->where(function ($q) use ($term) {
                $q->where('name', 'like', "%{$term}%")
                  ->orWhere('description', 'like', "%{$term}%")
                  ->orWhere('author', 'like', "%{$term}%")
                  ->orWhere('slug', 'like', "%{$term}%");
            });

        // Non-admin users can only see their own themes
        if (auth()->user()->role_id != 1) {
            $query->where('user_id', auth()->user()->id);
        }

        $themes = $query->paginate($itemsPerPage);

        if ($themes->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $themes;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    /**
     * Get single theme by ID
     */
    public function singleTheme(int $themeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $theme = Theme::with(['user', 'profiles'])->find($themeId);

        if ($theme) {
            // Check permissions
            if (auth()->user()->role_id != 1 && $theme->user_id != auth()->user()->id) {
                $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
                $serviceResult->Message = 'غير مصرح لك بالوصول لهذا الثيم';
                return $serviceResult;
            }

            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    /**
     * Get theme by slug
     */
    public function getThemeBySlug(string $slug): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $theme = Theme::where('slug', $slug)->where('is_active', true)->first();

        if ($theme) {
            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Message = 'الثيم غير موجود أو غير نشط';
        }

        return $serviceResult;
    }

    /**
     * Create new theme
     */
    public function createTheme(array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            // Generate unique slug
            $slug = Str::slug($data['name']);
            $originalSlug = $slug;
            $counter = 1;

            while (Theme::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $data['slug'] = $slug;
            $data['user_id'] = auth()->user()->id;
            $data['directory_path'] = "themes/{$data['type']}/{$slug}";

            $theme = Theme::create($data);

            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
            $serviceResult->Message = 'تم إنشاء الثيم بنجاح';

            // Log the action
            $this->logAction('create', 'Theme', $theme->id, 'تم إنشاء ثيم جديد: ' . $theme->name);

        } catch (\Exception $e) {
            $serviceResult->Message = 'فشل في إنشاء الثيم: ' . $e->getMessage();
            $this->logError('ThemeService@createTheme', $e->getMessage());
        }

        return $serviceResult;
    }

    /**
     * Update theme
     */
    public function updateTheme(int $themeId, array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            $theme = Theme::find($themeId);

            if (!$theme) {
                $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
                $serviceResult->Message = 'الثيم غير موجود';
                return $serviceResult;
            }

            // Check permissions
            if (auth()->user()->role_id != 1 && $theme->user_id != auth()->user()->id) {
                $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
                $serviceResult->Message = 'غير مصرح لك بتعديل هذا الثيم';
                return $serviceResult;
            }

            $theme->update($data);

            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
            $serviceResult->Message = 'تم تحديث الثيم بنجاح';

            // Log the action
            $this->logAction('update', 'Theme', $theme->id, 'تم تحديث الثيم: ' . $theme->name);

        } catch (\Exception $e) {
            $serviceResult->Message = 'فشل في تحديث الثيم: ' . $e->getMessage();
            $this->logError('ThemeService@updateTheme', $e->getMessage());
        }

        return $serviceResult;
    }

    /**
     * Delete theme
     */
    public function deleteTheme(int $themeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            $theme = Theme::find($themeId);

            if (!$theme) {
                $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
                $serviceResult->Message = 'الثيم غير موجود';
                return $serviceResult;
            }

            // Check permissions
            if (auth()->user()->role_id != 1 && $theme->user_id != auth()->user()->id) {
                $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
                $serviceResult->Message = 'غير مصرح لك بحذف هذا الثيم';
                return $serviceResult;
            }

            // Check if theme is being used
            if ($theme->profiles()->count() > 0) {
                $serviceResult->Message = 'لا يمكن حذف الثيم لأنه مستخدم في بروفايلات';
                return $serviceResult;
            }

            // Check if it's default theme
            if ($theme->is_default) {
                $serviceResult->Message = 'لا يمكن حذف الثيم الافتراضي';
                return $serviceResult;
            }

            $themeName = $theme->name;
            $theme->delete();

            $serviceResult->Status = true;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
            $serviceResult->Message = 'تم حذف الثيم بنجاح';

            // Log the action
            $this->logAction('delete', 'Theme', $themeId, 'تم حذف الثيم: ' . $themeName);

        } catch (\Exception $e) {
            $serviceResult->Message = 'فشل في حذف الثيم: ' . $e->getMessage();
            $this->logError('ThemeService@deleteTheme', $e->getMessage());
        }

        return $serviceResult;
    }

    /**
     * Get themes by type
     */
    public function getThemesByType(string $type): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $themes = Theme::byType($type)->active()->get();

        if ($themes->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $themes;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    /**
     * Get default theme for type
     */
    public function getDefaultTheme(string $type): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $theme = Theme::byType($type)->default()->active()->first();

        if ($theme) {
            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Message = 'لا يوجد ثيم افتراضي لهذا النوع';
        }

        return $serviceResult;
    }

    /**
     * Set theme as default
     */
    public function setAsDefault(int $themeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            $theme = Theme::find($themeId);

            if (!$theme) {
                $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
                $serviceResult->Message = 'الثيم غير موجود';
                return $serviceResult;
            }

            // Remove default from other themes of same type
            Theme::byType($theme->type)->update(['is_default' => false]);

            // Set this theme as default
            $theme->update(['is_default' => true]);

            $serviceResult->Status = true;
            $serviceResult->data = $theme;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
            $serviceResult->Message = 'تم تعيين الثيم كافتراضي بنجاح';

            // Log the action
            $this->logAction('update', 'Theme', $theme->id, 'تم تعيين الثيم كافتراضي: ' . $theme->name);

        } catch (\Exception $e) {
            $serviceResult->Message = 'فشل في تعيين الثيم كافتراضي: ' . $e->getMessage();
            $this->logError('ThemeService@setAsDefault', $e->getMessage());
        }

        return $serviceResult;
    }
}
