<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\Profile;
use App\Traits\LogsTrait;

class ProfessionalProfileService extends BaseService
{
    use LogsTrait;

    /**
     * إنشاء بروفايل مهني مع التأكد من فريدية slug
     */
    public function createProfileWithUniqueSlug(int $userId, string $username, ?string $name = null): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];

        try {
            // إنشاء slug بناءً على اسم المستخدم
            $baseSlug = \Illuminate\Support\Str::slug($username);
            $slug = $baseSlug;

            // التحقق من وجود slug في قاعدة البيانات
            $counter = 1;
            while (Profile::where('slug', $slug)->exists()) {
                $slug = $baseSlug . $counter;
                $counter++;
            }

            // استخدم اللغة الحالية للتطبيق
            $locale = app()->getLocale();

            // إعداد بيانات البروفايل
            $profileData = [
                'user_id' => $userId,
                'slug' => $slug,
                'name' => [
                    $locale => $name ?? $username,
                ],
                'visibility' => 'public',
                'is_active' => true,
            ];

            // إنشاء البروفايل
            $profile = Profile::create($profileData);

            if ($profile) {
                // تسجيل استخدام الميزة
                $user = \App\Models\User::find($userId);
                if ($user) {
                    $user->recordFeatureUsage('personal-profile', 1);
                }
                
                $serviceResult->Status = true;
                $serviceResult->data = $profile;
                $serviceResult->Code = StatusCodes::CREATED;
            } else {
                $serviceResult->Code = StatusCodes::OPERATION_FAILED;
                $serviceResult->data = 'Failed to create Profile';
            }
        } catch (\Exception $e) {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
            $serviceResult->data = $e->getMessage();

            $this->log($e->getMessage());
        }

        return $serviceResult;
    }
}