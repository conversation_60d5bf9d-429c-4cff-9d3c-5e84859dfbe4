<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\Setting;
use App\Traits\LogsTrait;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SettingService extends BaseService
{
    use AuthorizesRequests;
    use LogsTrait;

    public function getSettings(): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
        
        $settings = Setting::first();
        if ($settings) {
            $serviceResult->Status = true;
            $serviceResult->data = $settings;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function updateSettings(array $settingsData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        try {
            $settings = Setting::firstOrCreate([]);
            
            // تنظيف البيانات قبل الحفظ
            $cleanData = [];
            foreach ($settingsData as $key => $value) {
                if ($value !== null && ($value !== '' || is_bool($value))) {
                    $cleanData[$key] = $value;
                }
            }
            
            if ($settings->update($cleanData)) {
                // Clear all related caches
                \App\Helpers\SettingsHelper::clearCache();
                \Illuminate\Support\Facades\Cache::forget('home_data');
                \Illuminate\Support\Facades\Artisan::call('view:clear');
                
                $serviceResult->Status = true;
                $serviceResult->data = $settings->fresh();
                $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
            } else {
                $serviceResult->Code = StatusCodes::OPERATION_FAILED;
            }
        } catch (\Exception $e) {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    public function getSetting(string $key, $default = null): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
        
        $value = Setting::get($key, $default);
        $serviceResult->Status = true;
        $serviceResult->data = $value;
        $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    public function setSetting(string $key, $value): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $setting = Setting::set($key, $value);
        if ($setting) {
            $serviceResult->Status = true;
            $serviceResult->data = $setting;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }
}
