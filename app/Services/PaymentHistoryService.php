<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\Payment;

class PaymentHistoryService extends BaseService
{
    public function createPaymentRecord($data)
    {
        try {
            $payment = Payment::create([
                'user_id' => $data['user_id'],
                'subscription_id' => $data['subscription_id'] ?? null,
                'plan_id' => $data['plan_id'] ?? null,
                'payment_id' => $data['payment_id'],
                'gateway' => $data['gateway'],
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'USD',
                'status' => $data['status'] ?? 'pending',
                'type' => $data['type'] ?? 'subscription',
                'gateway_data' => $data['gateway_data'] ?? null,
                'metadata' => $data['metadata'] ?? null,
                'paid_at' => $data['paid_at'] ?? null,
            ]);

            return new ServiceResult(true, $payment, 201);
        } catch (\Exception $e) {
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }

    public function updatePaymentStatus($paymentId, $status, $gatewayData = null)
    {
        try {
            $payment = Payment::where('payment_id', $paymentId)->first();

            if (!$payment) {
                return new ServiceResult(false, 'Payment not found', 404);
            }

            $updateData = ['status' => $status];

            if ($status === 'completed' && !$payment->paid_at) {
                $updateData['paid_at'] = now();
            }

            if ($gatewayData) {
                $updateData['gateway_data'] = array_merge($payment->gateway_data ?? [], $gatewayData);
            }

            $payment->update($updateData);

            return new ServiceResult(true, $payment, 200);
        } catch (\Exception $e) {
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }

    public function getUserPayments($userId, $filters = [])
    {
        try {
            $query = Payment::with(['plan', 'subscription'])
                ->where('user_id', $userId);

            if (isset($filters['status']) && !empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (isset($filters['gateway']) && !empty($filters['gateway'])) {
                $query->where('gateway', $filters['gateway']);
            }

            if (isset($filters['type']) && !empty($filters['type'])) {
                $query->where('type', $filters['type']);
            }

            if (isset($filters['date_from']) && !empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && !empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            $payments = $query->orderBy('created_at', 'desc')
                ->paginate($filters['per_page'] ?? 15);

            return new ServiceResult(true, $payments, 200);
        } catch (\Exception $e) {
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }

    public function getPaymentStats($userId)
    {
        try {
            $stats = [
                'total_payments' => Payment::where('user_id', $userId)->count(),
                'completed_payments' => Payment::where('user_id', $userId)->where('status', 'completed')->count(),
                'total_amount' => Payment::where('user_id', $userId)->where('status', 'completed')->sum('amount'),
                'pending_payments' => Payment::where('user_id', $userId)->where('status', 'pending')->count(),
                'failed_payments' => Payment::where('user_id', $userId)->where('status', 'failed')->count(),
                'last_payment' => Payment::where('user_id', $userId)->where('status', 'completed')->latest()->first(),
                'monthly_stats' => $this->getMonthlyStats($userId),
                'gateway_stats' => $this->getGatewayStats($userId),
            ];

            return new ServiceResult(true, $stats, 200);
        } catch (\Exception $e) {
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }

    private function getMonthlyStats($userId)
    {
        return Payment::where('user_id', $userId)
            ->where('status', 'completed')
            ->selectRaw('MONTH(paid_at) as month, YEAR(paid_at) as year, COUNT(*) as count, SUM(amount) as total')
            ->whereYear('paid_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
    }

    private function getGatewayStats($userId)
    {
        return Payment::where('user_id', $userId)
            ->where('status', 'completed')
            ->selectRaw('gateway, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('gateway')
            ->get();
    }

    public function getPaymentDetails($paymentId, $userId = null)
    {
        try {
            $query = Payment::with(['user', 'plan', 'subscription']);

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $payment = $query->where('payment_id', $paymentId)->first();

            if (!$payment) {
                return new ServiceResult(false, 'Payment not found', 404);
            }

            return new ServiceResult(true, $payment, 200);
        } catch (\Exception $e) {
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }

    public function getAllPayments($filters = []): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            $query = Payment::with(['user', 'plan', 'subscription']);

            if (isset($filters['status']) && !empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (isset($filters['gateway']) && !empty($filters['gateway'])) {
                $query->where('gateway', $filters['gateway']);
            }

            if (isset($filters['user_id']) && !empty($filters['user_id'])) {
                $query->where('user_id', $filters['user_id']);
            }

            if (isset($filters['search']) && !empty($filters['search'])) {
                $query->whereHas('user', function ($q) use ($filters) {
                    $q->where('name', 'like', '%'.$filters['search'].'%')
                      ->orWhere('email', 'like', '%'.$filters['search'].'%');
                });
            }

            if (isset($filters['date_from']) && !empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && !empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            $payments = $query->orderBy('created_at', 'desc')
                ->paginate($filters['per_page'] ?? 15);

            $serviceResult->Status = true;
            $serviceResult->data = $payments;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } catch (\Exception $e) {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function getAdminStats(): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        try {
            $stats = [
                'total_payments' => Payment::count(),
                'completed_payments' => Payment::where('status', 'completed')->count(),
                'total_amount' => Payment::where('status', 'completed')->sum('amount'),
                'pending_payments' => Payment::where('status', 'pending')->count(),
                'failed_payments' => Payment::where('status', 'failed')->count(),
                'total_users' => Payment::distinct('user_id')->count(),
                'last_payment' => Payment::with('user')->latest()->first(),
                'monthly_stats' => $this->getAdminMonthlyStats(),
                'gateway_stats' => $this->getAdminGatewayStats(),
            ];

            $serviceResult->Status = true;
            $serviceResult->data = $stats;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } catch (\Exception $e) {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    private function getAdminMonthlyStats()
    {
        return Payment::where('status', 'completed')
            ->selectRaw('MONTH(paid_at) as month, YEAR(paid_at) as year, COUNT(*) as count, SUM(amount) as total')
            ->whereYear('paid_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
    }

    private function getAdminGatewayStats()
    {
        return Payment::where('status', 'completed')
            ->selectRaw('gateway, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('gateway')
            ->get();
    }
}
