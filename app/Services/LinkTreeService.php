<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\LinkTree;
use App\Models\LinkTreeLink;
use App\Models\Social;
use App\Traits\LogsTrait;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class LinkTreeService extends BaseService
{
    use AuthorizesRequests;
    use LogsTrait;

    public function allLinkTrees(int $itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
        $query = LinkTree::select();
        if (auth()->user()->role_id != 1) {
            $query->where('user_id', auth()->user()->id);
        }
        if ($trashed) {
            $query->onlyTrashed();
        }
        $linkTrees = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);
        if ($linkTrees->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTrees;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function singleLinkTree(int $linkTreeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;
        $linkTree = LinkTree::with('links', 'socialMedia')->find($linkTreeId);
        if ($linkTree) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function searchLinkTree(string $term, int $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $linkTrees = LinkTree::where(function ($query) use ($term) {
            $query->where('name', 'like', "%$term%")
                ->orWhere('description', 'like', "%$term%")
                ->orWhere('job_title', 'like', "%$term%");
        })
            ->paginate($itemsPerPage);
        if ($linkTrees->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTrees;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    // public function updateLinkTree(int $linkTreeId, $linkTreeData): ServiceResult
    // {
    //     $serviceResult = new ServiceResult();
    //     $serviceResult->Status = false;
    //     $serviceResult->data = [];
    //     $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

    //     $linkTree = LinkTree::find($linkTreeId);
    //     // $this->authorize('update', $linkTree);
    //     if (!$linkTree) {
    //         $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

    //         return $serviceResult;
    //     }

    //     $links = $linkTreeData['links'] ?? [];
    //     unset($linkTreeData['links']);

    //     $linkTree->fill($linkTreeData);
    //     if ($linkTree->save()) {
    //         if (!empty($links)) {
    //             foreach ($links as $link) {
    //                 $linkTreeLink = new LinkTreeLink();
    //                 $linkTreeLink->link_tree_id = $linkTree->id;
    //                 $linkTreeLink->fill($link);
    //                 $linkTreeLink->save();
    //             }
    //         }

    //         $linkTree->load('links');
    //         $serviceResult->Status = true;
    //         $serviceResult->data = $linkTree;
    //         $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
    //     } else {
    //         $serviceResult->Code = StatusCodes::OPERATION_FAILED;
    //     }

    //     return $serviceResult;
    // }
    public function updateLinkTree(int $linkTreeId, $linkTreeData): ServiceResult
    {
        // dd($linkTreeId);
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $linkTree = LinkTree::find($linkTreeId);
        // $this->authorize('update', $linkTree);
        if (!$linkTree) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        $links = $linkTreeData['links'] ?? [];
        $socialMedia = $linkTreeData['social_media'] ?? [];
        unset($linkTreeData['links'], $linkTreeData['social_media']);

        if (isset($linkTreeData['avatar'])) {
            $linkTree->clearMediaCollection('avatar');
            $linkTree->addMedia($linkTreeData['avatar'])->toMediaCollection('avatar');
            unset($linkTreeData['avatar']);
        }

        if (isset($linkTreeData['cover'])) {
            $linkTree->clearMediaCollection('cover');
            $linkTree->addMedia($linkTreeData['cover'])->toMediaCollection('cover');
            unset($linkTreeData['cover']);
        }

        $linkTree->fill($linkTreeData);
        if ($linkTree->save()) {
            // Handle Links
            $existingLinkIds = collect($links)->pluck('id')->filter()->toArray();
            LinkTreeLink::where('link_tree_id', $linkTree->id)
                ->whereNotIn('id', $existingLinkIds)
                ->delete();
            if (!empty($links)) {
                foreach ($links as $link) {
                    if (!empty($link['id'])) {
                        $linkTreeLink = LinkTreeLink::find($link['id']);
                        if ($linkTreeLink) {
                            $linkTreeLink->fill([
                                'name' => $link['name'],
                                'url' => $link['url'],
                                'target' => $link['target'],
                                'order' => $link['order'],
                                'is_active' => $link['is_active'],
                            ]);
                            $linkTreeLink->save();
                        }
                    } else {
                        $linkTreeLink = new LinkTreeLink();
                        $linkTreeLink->link_tree_id = $linkTree->id;
                        $linkTreeLink->fill([
                            'name' => $link['name'],
                            'url' => $link['url'],
                            'target' => $link['target'],
                            'order' => $link['order'],
                            'is_active' => $link['is_active'],
                        ]);
                        $linkTreeLink->save();
                    }
                }
            }

            // Handle Social Media
            $existingSocialIds = collect($socialMedia)->pluck('id')->filter()->toArray();
            // Delete social media entries not in the updated list
            Social::where('user_id', $linkTree->user_id)
                ->where('type', 'link_tree')
                ->where('relationship_id', $linkTree->id)
                ->whereNotIn('id', $existingSocialIds)
                ->delete();

            foreach ($socialMedia as $social) {
                $socialModel = !empty($social['id']) ? Social::find($social['id']) : new Social();

                if ($socialModel) {
                    $socialModel->fill([
                        'user_id' => $linkTree->user_id,
                        'relationship_id' => $linkTree->id,
                        'name' => $social['platform'],
                        'url' => $social['url'],
                        'order' => $social['order'],
                        'is_active' => $social['is_active'],
                        'type' => 'link_tree',
                    ]);
                    $socialModel->save();
                }
            }

            $linkTree->load(['links', 'socialMedia']);
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function createLinkTree(array $linkTreeData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $links = $linkTreeData['links'] ?? [];
        $socialMedia = $linkTreeData['social_media'] ?? [];
        unset($linkTreeData['links'], $linkTreeData['social_media']);

        $linkTree = new LinkTree();

        if (isset($linkTreeData['avatar'])) {
            $linkTree->addMedia($linkTreeData['avatar'])->toMediaCollection('avatar');
            unset($linkTreeData['avatar']);
        }

        if (isset($linkTreeData['cover'])) {
            $linkTree->addMedia($linkTreeData['cover'])->toMediaCollection('cover');
            unset($linkTreeData['cover']);
        }

        $linkTree->fill($linkTreeData);

        if ($linkTree->save()) {
            // Handle Links
            if (!empty($links)) {
                foreach ($links as $link) {
                    $linkTreeLink = new LinkTreeLink();
                    $linkTreeLink->link_tree_id = $linkTree->id;
                    $linkTreeLink->fill([
                        'name' => $link['name'],
                        'url' => $link['url'],
                        'target' => $link['target'],
                        'order' => $link['order'],
                        'is_active' => $link['is_active'],
                    ]);
                    $linkTreeLink->save();
                }
            }

            // Handle Social Media
            if (!empty($socialMedia)) {
                foreach ($socialMedia as $social) {
                    $socialModel = new Social();
                    $socialModel->fill([
                        'user_id' => $linkTree->user_id,
                        'relationship_id' => $linkTree->id,
                        'name' => $social['platform'],
                        'url' => $social['url'],
                        'order' => $social['order'],
                        'is_active' => $social['is_active'],
                        'type' => 'link_tree',
                    ]);
                    $socialModel->save();
                }
            }

            $linkTree->load(['links', 'socialMedia']);
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function deleteItem(int $linkTreeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $linkTree = LinkTree::find($linkTreeId);
        if (!$linkTree) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($linkTree->delete()) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function restoreItem(int $linkTreeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $linkTree = LinkTree::onlyTrashed()->find($linkTreeId);
        if (!$linkTree) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($linkTree->restore()) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function forceDeleteItem(int $linkTreeId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $linkTree = LinkTree::onlyTrashed()->find($linkTreeId);

        if (!$linkTree) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($linkTree->forceDelete()) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTree;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function allLinkTreesApi(string $orderBy = 'id', string $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $query = LinkTree::with('links')->where('is_active', 1);
        $linkTrees = $query->orderBy($orderBy, $sortBy)
            ->get();
        if ($linkTrees->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $linkTrees;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    /**
     * إنشاء LinkTree مع التأكد من فريدية slug.
     *
     * @param int    $userId   معرف المستخدم
     * @param string $username اسم المستخدم الذي سيستخدم كأساس للـ slug
     * @param string $name     الاسم المعروض للـ LinkTree
     */
    public function createLinkTreeWithUniqueSlug(int $userId, string $username, ?string $name = null): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];

        try {
            // إنشاء slug بناءً على اسم المستخدم
            $baseSlug = \Illuminate\Support\Str::slug($username);
            $slug = $baseSlug;

            // التحقق من وجود slug في قاعدة البيانات
            $counter = 1;
            while (LinkTree::where('slug', $slug)->exists()) {
                $slug = $baseSlug.$counter;
                ++$counter;
            }

            // استخدم اللغة الحالية للتطبيق
            $locale = app()->getLocale();

            // إعداد بيانات LinkTree
            $linkTreeData = [
                'user_id' => $userId,
                'slug' => $slug,
                'name' => [
                    $locale => $name ?? $username,
                ],
                'is_active' => true,
            ];

            // إنشاء LinkTree
            $linkTree = LinkTree::create($linkTreeData);

            if ($linkTree) {
                // تسجيل استخدام الميزة
                $user = \App\Models\User::find($userId);
                if ($user) {
                    $user->recordFeatureUsage('create-link-tree', 1);
                }
                
                $serviceResult->Status = true;
                $serviceResult->data = $linkTree;
                $serviceResult->Code = StatusCodes::CREATED;
            } else {
                $serviceResult->Code = StatusCodes::OPERATION_FAILED;
                $serviceResult->data = 'Failed to create LinkTree';
            }
        } catch (\Exception $e) {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
            $serviceResult->data = $e->getMessage();

            // يمكن إضافة سجل للأخطاء هنا
            $this->log($e->getMessage());
        }

        return $serviceResult;
    }
}
