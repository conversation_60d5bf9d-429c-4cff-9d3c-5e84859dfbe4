<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\FeatureUsage;
use App\Models\Subscription;
use App\Models\Plan;
use App\Models\User;
use Carbon\Carbon;

class SubscriptionService
{
    public function createSubscription(array $data): ServiceResult
    {
        $result = new ServiceResult();

        try {
            $subscription = Subscription::create([
                'user_id' => $data['user_id'],
                'plan_id' => $data['plan_id'],
                'starts_at' => $data['starts_at'] ?? now(),
                'ends_at' => $this->calculateEndDate($data),
                'trial_ends_at' => $data['trial_ends_at'] ?? null,
                'status' => 'active',
                'auto_renew' => $data['auto_renew'] ?? true,
            ]);

            $result->data = $subscription;
            $result->Code = StatusCodes::CREATED;
            $result->Status = true;
        } catch (\Exception $e) {
            $result->Code = StatusCodes::OPERATION_FAILED;
            $result->data = $e->getMessage();
        }

        return $result;
    }

    private function calculateEndDate(array $data): Carbon
    {
        $interval = $data['billing_interval'] ?? 'month';
        $count = $data['billing_interval_count'] ?? 1;

        return match ($interval) {
            'day' => now()->addDays($count),
            'week' => now()->addWeeks($count),
            'month' => now()->addMonths($count),
            'year' => now()->addYears($count),
            default => now()->addMonth()
        };
    }

    public function recordFeatureUsage(User $user, string $featureSlug, int $quantity = 1): ServiceResult
    {
        $result = new ServiceResult();

        try {
            $user->recordFeatureUsage($featureSlug, $quantity);
            
            $result->Code = StatusCodes::CREATED;
            $result->Status = true;
        } catch (\Exception $e) {
            $result->Code = StatusCodes::OPERATION_FAILED;
            $result->data = $e->getMessage();
        }

        return $result;
    }

    /**
     * إنشاء اشتراك جديد للمستخدم وإنشاء LinkTree إذا كانت الخطة مجانية
     *
     * @param User $user المستخدم الذي سيتم إنشاء الاشتراك له
     * @param int $planId معرف الخطة
     * @param array $options خيارات إضافية للاشتراك
     * @return ServiceResult
     */
    public function subscribeUserToPlan(User $user, int $planId, array $options = []): ServiceResult
    {
        $result = new ServiceResult();
        $result->Status = false;

        try {
            // التحقق من وجود اشتراك نشط للمستخدم بنفس الخطة
            $existingSubscription = Subscription::where('user_id', $user->id)
                ->where('plan_id', $planId)
                ->where('status', 'active')
                ->where('ends_at', '>', now())
                ->first();

            if ($existingSubscription) {
                $result->Code = StatusCodes::ALREADY_EXISTS;
                $result->data = 'User already has an active subscription for this plan';
                return $result;
            }

            // الحصول على خطة الاشتراك
            $plan = Plan::findOrFail($planId);

            // بيانات الاشتراك
            $subscriptionData = [
                'user_id' => $user->id,
                'plan_id' => $planId,
                'starts_at' => $options['starts_at'] ?? now(),
                'billing_interval' => $plan->billing_interval,
                'billing_interval_count' => $plan->billing_interval_count,
                'auto_renew' => $options['auto_renew'] ?? true,
                'trial_ends_at' => $options['trial_ends_at'] ?? null,
            ];

            // إنشاء الاشتراك
            $subscriptionResult = $this->createSubscription($subscriptionData);

            if (!$subscriptionResult->Status) {
                return $subscriptionResult;
            }

            // ربط الدفع بالاشتراك إذا وجد
            if (isset($options['payment_id']) && $subscriptionResult->data) {
                $paymentService = new PaymentService();
                $paymentService->linkPaymentToSubscription($options['payment_id'], $subscriptionResult->data->id);
            }

            // إذا كانت الخطة مجانية، قم بإنشاء LinkTree للمستخدم
            if ($plan->price == 0) {
                $linkTreeService = new LinkTreeService();
                $linkTreeResult = $linkTreeService->createLinkTreeWithUniqueSlug(
                    $user->id,
                    $user->username,
                    $user->name
                );

                if ($linkTreeResult->Status) {
                    $subscriptionResult->data->linkTree = $linkTreeResult->data;
                }
            }

            // التحقق من ميزات الخطة وإنشاء البروفايل المهني إذا كانت متاحة
            $planFeatures = $plan->planFeatures()->with('feature')->get();
            $hasProfileFeature = false;
            
            foreach ($planFeatures as $planFeature) {
                if ($planFeature->allowed_limit > 0 || $planFeature->allowed_limit == -1) {
                    $slugRaw = $planFeature->feature->getRawOriginal('slug');
                    $slugArray = json_decode($slugRaw, true);
                    $slug = $slugArray['en'] ?? '';
                    
                    if (in_array($slug, ['profiles', 'professional-profiles', 'profile', 'personal-profile'])) {
                        $hasProfileFeature = true;
                        break;
                    }
                }
            }
            
            if ($hasProfileFeature) {
                $profileService = new ProfessionalProfileService();
                $profileResult = $profileService->createProfileWithUniqueSlug(
                    $user->id,
                    $user->username,
                    $user->name
                );

                if ($profileResult->Status) {
                    $subscriptionResult->data->profile = $profileResult->data;
                }
            }

            // تحديث الجلسة لإظهار الميزات الجديدة
            if ($subscriptionResult->Status) {
                \App\Helpers\SubscriptionHelper::storeAvailableFeaturesInSession($user);
            }

            return $subscriptionResult;

        } catch (\Exception $e) {
            $result->Code = StatusCodes::OPERATION_FAILED;
            $result->data = $e->getMessage();
            return $result;
        }
    }

    // دوال أخرى لإدارة الاشتراكات
}
