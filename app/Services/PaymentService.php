<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Models\Payment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentService extends BaseService
{
    private $paypalClientId;
    private $paypalSecret;
    private $paypalMode;
    private $paypalBaseUrl;
    
    public function __construct()
    {
        // Initialize PayPal settings (will be checked when needed)
        $this->initializePayPalSettings();
    }
    
    private function initializePayPalSettings()
    {
        $this->paypalClientId = \App\Helpers\SettingsHelper::getSetting('paypal_client_id');
        $this->paypalSecret = \App\Helpers\SettingsHelper::getSetting('paypal_client_secret');
        
        $isSandbox = \App\Helpers\SettingsHelper::getSetting('paypal_sandbox', false);
        $this->paypalMode = $isSandbox ? 'sandbox' : 'live';
        $this->paypalBaseUrl = $this->paypalMode === 'live' 
            ? 'https://api.paypal.com' 
            : 'https://api.sandbox.paypal.com';
    }
    
    public function processPayment($amount, $user, $planId, $gateway = 'paypal')
    {
        try {
            switch ($gateway) {
                case 'paypal':
                    return $this->processPayPalPayment($amount, $user, $planId);
                case 'stripe':
                    return $this->processStripePayment($amount, $user, $planId);
                case 'paymob':
                    return $this->processPaymobPayment($amount, $user, $planId);
                default:
                    return new ServiceResult(false, __('Unsupported payment gateway'), 400);
            }
        } catch (\Exception $e) {
            Log::error('Payment Error: ' . $e->getMessage());
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }
    
    private function processPayPalPayment($amount, $user, $planId)
    {
        // Check if PayPal is enabled and configured
        $paypalEnabled = \App\Helpers\SettingsHelper::getSetting('payment_paypal_enabled', false);
        if (!$paypalEnabled) {
            return new ServiceResult(false, __('PayPal is not enabled'), 400);
        }
        
        if (!$this->paypalClientId || !$this->paypalSecret) {
            return new ServiceResult(false, __('PayPal settings are incomplete'), 400);
        }
        
        $accessToken = $this->getPayPalAccessToken();
        if (!$accessToken) {
            return new ServiceResult(false, __('Failed to get access token'), 500);
        }
        
        $order = $this->createPayPalOrder($accessToken, $amount, $user, $planId);
        Log::info('PayPal Order Response', ['order' => $order]);
        
        if (!$order) {
            Log::error('PayPal Order Creation Failed - No Response');
            return new ServiceResult(false, __('Failed to create payment order'), 500);
        }
        
        if (!isset($order['links'])) {
            Log::error('PayPal Order Missing Links', ['order' => $order]);
            return new ServiceResult(false, __('Failed to get payment links'), 500);
        }
        
        $approvalUrl = null;
        if (isset($order['links']) && is_array($order['links'])) {
            foreach ($order['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'approve') {
                    $approvalUrl = $link['href'];
                    break;
                }
            }
        }
        
        Log::info('PayPal Approval URL Search', [
            'found_url' => $approvalUrl,
            'links' => $order['links'] ?? []
        ]);
        if (!$approvalUrl) {
            Log::error('PayPal Approval URL Not Found', ['links' => $order['links']]);
            return new ServiceResult(false, __('Failed to get payment URL'), 500);
        }
        
        // حفظ سجل الدفع
        $this->createPaymentRecord([
            'user_id' => $user->id,
            'plan_id' => $planId,
            'payment_id' => $order['id'],
            'gateway' => 'paypal',
            'amount' => $amount,
            'currency' => 'USD',
            'status' => 'pending',
            'type' => 'subscription',
            'gateway_data' => $order,
        ]);
        
        $resultData = [
            'payment_id' => $order['id'] ?? null,
            'redirect_url' => $approvalUrl,
            'order_data' => $order
        ];
        
        Log::info('PayPal Service Result', ['result_data' => $resultData]);
        
        return new ServiceResult(true, $resultData, 200);
    }
    
    private function processStripePayment($amount, $user, $planId)
    {
        // Check if Stripe is enabled and configured
        $stripeEnabled = \App\Helpers\SettingsHelper::getSetting('payment_stripe_enabled', false);
        if (!$stripeEnabled) {
            return new ServiceResult(false, __('Stripe is not enabled'), 400);
        }
        
        $publicKey = \App\Helpers\SettingsHelper::getSetting('stripe_public_key');
        $secretKey = \App\Helpers\SettingsHelper::getSetting('stripe_secret_key');
        
        if (!$publicKey || !$secretKey) {
            return new ServiceResult(false, __('Stripe settings are incomplete'), 400);
        }
        
        try {
            // Create Stripe checkout session
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
                'Content-Type' => 'application/x-www-form-urlencoded'
            ])->asForm()->post('https://api.stripe.com/v1/checkout/sessions', [
                'payment_method_types[]' => 'card',
                'line_items[0][price_data][currency]' => 'usd',
                'line_items[0][price_data][product_data][name]' => 'Subscription Plan ' . $planId,
                'line_items[0][price_data][unit_amount]' => $amount * 100,
                'line_items[0][quantity]' => 1,
                'mode' => 'payment',
                'success_url' => url('/payment/success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => url('/payment/cancel'),
                'customer_email' => $user->email,
            ]);
            
            if ($response->successful()) {
                $session = $response->json();
                
                // حفظ سجل الدفع
                $this->createPaymentRecord([
                    'user_id' => $user->id,
                    'plan_id' => $planId,
                    'payment_id' => $session['id'],
                    'gateway' => 'stripe',
                    'amount' => $amount,
                    'currency' => 'USD',
                    'status' => 'pending',
                    'type' => 'subscription',
                    'gateway_data' => $session,
                ]);
                
                return new ServiceResult(true, [
                    'payment_id' => $session['id'],
                    'redirect_url' => $session['url'],
                    'session_data' => $session
                ], 200);
            }
            
            Log::error('Stripe Session Creation Failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return new ServiceResult(false, __('Failed to create Stripe payment session'), 500);
            
        } catch (\Exception $e) {
            Log::error('Stripe Payment Error: ' . $e->getMessage());
            return new ServiceResult(false, __('Error processing payment via Stripe') . ': ' . $e->getMessage(), 500);
        }
    }
    
    private function processPaymobPayment($amount, $user, $planId)
    {
        // Check if Paymob is enabled and configured
        $paymobEnabled = \App\Helpers\SettingsHelper::getSetting('payment_paymob_enabled', false);
        if (!$paymobEnabled) {
            return new ServiceResult(false, __('Paymob is not enabled'), 400);
        }
        
        $setting = \App\Models\Setting::first();
        $secretKey = $setting->paymob_secret_key ?? null;
        $integrationId = $setting->paymob_integration_id ?? null;
        
        if (!$secretKey || !$integrationId) {
            return new ServiceResult(false, __('Paymob settings are incomplete'), 400);
        }
        
        try {
            // Step 1: Use secret key as auth token for UAE Paymob
            $authToken = $secretKey;
            Log::info('Using Paymob Secret Key as auth token');
            
            // Step 2: Create order
            $order = $this->createPaymobOrder($authToken, $amount, $user, $planId);
            if (!$order) {
                return new ServiceResult(false, __('Failed to create payment order in Paymob'), 500);
            }
            
            // Step 3: Get payment key
            $paymentKey = $this->getPaymobPaymentKey($authToken, $order['id'], $amount, $user, $integrationId);
            if (!$paymentKey) {
                return new ServiceResult(false, __('Failed to get payment key from Paymob'), 500);
            }
            
            // Step 4: Generate payment URL
            $iframeId = \App\Helpers\SettingsHelper::getSetting('paymob_iframe_id');
            if (!$iframeId) {
                return new ServiceResult(false, __('Iframe ID is not configured in Paymob settings'), 500);
            }
            
            $paymentUrl = "https://uae.paymob.com/api/acceptance/iframes/" . $iframeId . "?payment_token=" . $paymentKey;
            
            Log::info('Paymob Payment URL Generated', [
                'iframe_id' => $iframeId,
                'payment_url' => $paymentUrl
            ]);
            
            // حفظ سجل الدفع
            $this->createPaymentRecord([
                'user_id' => $user->id,
                'plan_id' => $planId,
                'payment_id' => $order['id'],
                'gateway' => 'paymob',
                'amount' => $amount,
                'currency' => 'AED',
                'status' => 'pending',
                'type' => 'subscription',
                'gateway_data' => $order,
            ]);
            
            return new ServiceResult(true, [
                'payment_id' => $order['id'],
                'redirect_url' => $paymentUrl,
                'order_data' => $order
            ], 200);
            
        } catch (\Exception $e) {
            Log::error('Paymob Payment Error: ' . $e->getMessage());
            return new ServiceResult(false, __('Error processing payment via Paymob') . ': ' . $e->getMessage(), 500);
        }
    }
    
    private function getPaymobAuthToken($apiKey)
    {
        try {
            Log::info('Paymob Auth Request', ['api_key_length' => strlen($apiKey)]);
            
            // Check if API key is base64 encoded JWT token
            $decodedKey = base64_decode($apiKey, true);
            if ($decodedKey && strpos($decodedKey, '.') !== false) {
                // This is a JWT token for UAE Paymob, use it directly
                Log::info('Using JWT token directly for UAE Paymob');
                return $decodedKey;
            }
            
            // Otherwise, try the traditional auth endpoint
            $response = Http::timeout(30)->post('https://uae.paymob.com/api/auth/tokens', [
                'api_key' => $apiKey
            ]);
            
            Log::info('Paymob Auth Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['token'])) {
                    Log::info('Paymob Auth Token Retrieved Successfully');
                    return $data['token'];
                }
                Log::error('Paymob Auth Response Missing Token', ['response' => $data]);
            } else {
                // Handle specific error cases
                $errorBody = $response->json();
                if ($response->status() === 403 && isset($errorBody['detail']) && $errorBody['detail'] === 'incorrect credentials') {
                    Log::error('Paymob Auth Failed - Incorrect Credentials');
                } else {
                    Log::error('Paymob Auth Failed', [
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                }
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Paymob Auth Token Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function createPaymobOrder($authToken, $amount, $user, $planId)
    {
        try {
            $orderData = [
                'auth_token' => $authToken,
                'delivery_needed' => false,
                'amount_cents' => $amount * 100,
                'currency' => 'AED',
                'items' => []
            ];
            
            Log::info('Paymob Order Creation Request', $orderData);
            
            $response = Http::timeout(30)->post('https://uae.paymob.com/api/ecommerce/orders', $orderData);
            
            Log::info('Paymob Order Creation Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Paymob Order Creation Failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Paymob Create Order Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function getPaymobPaymentKey($authToken, $orderId, $amount, $user, $integrationId)
    {
        try {
            $response = Http::post('https://uae.paymob.com/api/acceptance/payment_keys', [
                'auth_token' => $authToken,
                'amount_cents' => $amount * 100,
                'expiration' => 3600,
                'order_id' => $orderId,
                'billing_data' => [
                    'apartment' => 'NA',
                    'email' => $user->email,
                    'floor' => 'NA',
                    'first_name' => $user->name,
                    'street' => 'NA',
                    'building' => 'NA',
                    'phone_number' => $user->phone ?? 'NA',
                    'shipping_method' => 'NA',
                    'postal_code' => 'NA',
                    'city' => 'NA',
                    'country' => 'NA',
                    'last_name' => 'NA',
                    'state' => 'NA'
                ],
                'currency' => 'AED',
                'integration_id' => $integrationId
            ]);
            
            if ($response->successful()) {
                return $response->json()['token'];
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Paymob Payment Key Error: ' . $e->getMessage());
            return null;
        }
    }
    
    public function verifyPayment($request, $gateway = 'paypal')
    {
        try {
            switch ($gateway) {
                case 'paypal':
                    return $this->verifyPayPalPayment($request);
                case 'stripe':
                    return $this->verifyStripePayment($request);
                case 'paymob':
                    return $this->verifyPaymobPayment($request);
                default:
                    return new ServiceResult(false, __('Payment gateway not supported for verification'), 400);
            }
        } catch (\Exception $e) {
            Log::error('Payment Verification Error: ' . $e->getMessage());
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }
    
    private function verifyPayPalPayment($request)
    {
        try {
            $paymentId = $request->get('token');
            $payerId = $request->get('PayerID');
            
            Log::info('PayPal Verification Started', [
                'payment_id' => $paymentId,
                'payer_id' => $payerId
            ]);
            
            if (!$paymentId || !$payerId) {
                Log::error('PayPal Verification Failed - Missing Data');
                return new ServiceResult(false, __('Payment data is incomplete'), 400);
            }
            
            $accessToken = $this->getPayPalAccessToken();
            if (!$accessToken) {
                Log::error('PayPal Verification Failed - No Access Token');
                return new ServiceResult(false, __('Failed to verify payment'), 500);
            }
            
            Log::info('PayPal Access Token Retrieved Successfully');
            
            // Get order details to check if payment was approved
            $orderDetails = $this->getPayPalOrderDetails($accessToken, $paymentId);
            Log::info('PayPal Order Details', ['order_details' => $orderDetails]);
            
            if (!$orderDetails) {
                Log::error('PayPal Verification Failed - No Order Details');
                return new ServiceResult(false, __('Failed to get order details'), 500);
            }
            
            $orderStatus = $orderDetails['status'] ?? '';
            Log::info('PayPal Order Status', ['status' => $orderStatus]);
            
            // If order is approved, consider payment successful for sandbox
            if ($orderStatus === 'APPROVED') {
                Log::info('PayPal Payment Approved Successfully');
                
                // تحديث حالة الدفع
                $this->updatePaymentStatus($paymentId, 'completed', $orderDetails);
                
                return new ServiceResult(true, [
                    'success' => true,
                    'payment_id' => $paymentId,
                    'payer_id' => $payerId,
                    'order_data' => $orderDetails
                ], 200);
            }
            
            // Try to capture if approved
            if ($orderStatus === 'APPROVED') {
                $captureResult = $this->capturePayPalOrder($accessToken, $paymentId);
                Log::info('PayPal Capture Result', ['capture_result' => $captureResult]);
                
                if ($captureResult && isset($captureResult['status']) && $captureResult['status'] === 'COMPLETED') {
                    Log::info('PayPal Payment Captured Successfully');
                    
                    // تحديث حالة الدفع
                    $this->updatePaymentStatus($paymentId, 'completed', $captureResult);
                    
                    return new ServiceResult(true, [
                        'success' => true,
                        'payment_id' => $paymentId,
                        'payer_id' => $payerId,
                        'capture_data' => $captureResult
                    ], 200);
                }
            }
            
            Log::error('PayPal Payment Not Approved', ['status' => $orderStatus]);
            return new ServiceResult(false, __('Payment was not approved - Status') . ': ' . $orderStatus, 400);
            
        } catch (\Exception $e) {
            Log::error('PayPal Verification Error: ' . $e->getMessage());
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }
    
    private function verifyStripePayment($request)
    {
        try {
            $sessionId = $request->get('session_id');
            
            if (!$sessionId) {
                return new ServiceResult(false, __('Stripe session ID is missing'), 400);
            }
            
            $secretKey = \App\Helpers\SettingsHelper::getSetting('stripe_secret_key');
            
            // Retrieve the session from Stripe
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey
            ])->get('https://api.stripe.com/v1/checkout/sessions/' . $sessionId);
            
            if ($response->successful()) {
                $session = $response->json();
                
                if ($session['payment_status'] === 'paid') {
                    // تحديث حالة الدفع
                    $this->updatePaymentStatus($sessionId, 'completed', $session);
                    
                    return new ServiceResult(true, [
                        'success' => true,
                        'session_id' => $sessionId,
                        'payment_data' => $session
                    ], 200);
                }
                
                return new ServiceResult(false, __('Payment not completed'), 400);
            }
            
            return new ServiceResult(false, __('Failed to verify Stripe payment'), 500);
            
        } catch (\Exception $e) {
            Log::error('Stripe Verification Error: ' . $e->getMessage());
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }
    
    private function verifyPaymobPayment($request)
    {
        try {
            // Paymob sends transaction data via callback
            $transactionId = $request->get('id');
            $success = $request->get('success');
            $hmac = $request->get('hmac');
            
            Log::info('Paymob Verification Started', [
                'transaction_id' => $transactionId,
                'success' => $success,
                'hmac_provided' => !empty($hmac)
            ]);
            
            if (!$transactionId) {
                return new ServiceResult(false, __('Transaction ID is missing'), 400);
            }
            
            // Verify HMAC if configured
            $hmacSecret = \App\Helpers\SettingsHelper::getSetting('paymob_hmac_secret');
            if ($hmacSecret && $hmac) {
                $calculatedHmac = $this->calculatePaymobHmac($request->all(), $hmacSecret);
                if ($calculatedHmac !== $hmac) {
                    Log::error('Paymob HMAC Verification Failed');
                    return new ServiceResult(false, __('Failed to verify data integrity'), 400);
                }
            }
            
            if ($success === 'true' || $success === true) {
                Log::info('Paymob Payment Verified Successfully');
                
                // تحديث حالة الدفع
                $this->updatePaymentStatus($transactionId, 'completed', $request->all());
                
                return new ServiceResult(true, [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'payment_data' => $request->all()
                ], 200);
            }
            
            Log::error('Paymob Payment Failed', ['success' => $success]);
            return new ServiceResult(false, __('Payment failed via Paymob'), 400);
            
        } catch (\Exception $e) {
            Log::error('Paymob Verification Error: ' . $e->getMessage());
            return new ServiceResult(false, $e->getMessage(), 500);
        }
    }
    
    private function calculatePaymobHmac($data, $secret)
    {
        // Paymob HMAC calculation logic
        ksort($data);
        $hmacData = '';
        foreach ($data as $key => $value) {
            if ($key !== 'hmac') {
                $hmacData .= $value;
            }
        }
        return hash_hmac('sha512', $hmacData, $secret);
    }
    
    private function createPaymentRecord($data)
    {
        try {
            $payment = Payment::create($data);
            Log::info('Payment record created successfully', [
                'payment_id' => $payment->payment_id,
                'user_id' => $payment->user_id,
                'amount' => $payment->amount
            ]);
            return $payment;
        } catch (\Exception $e) {
            Log::error('Failed to create payment record: ' . $e->getMessage(), $data);
            return null;
        }
    }
    
    public function linkPaymentToSubscription($paymentId, $subscriptionId)
    {
        try {
            $payment = Payment::where('payment_id', $paymentId)->first();
            if ($payment) {
                $payment->update(['subscription_id' => $subscriptionId]);
                Log::info('Payment linked to subscription', [
                    'payment_id' => $paymentId,
                    'subscription_id' => $subscriptionId
                ]);
                return true;
            }
            Log::error('Payment not found for linking', ['payment_id' => $paymentId]);
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to link payment to subscription: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
                'subscription_id' => $subscriptionId
            ]);
            return false;
        }
    }
    
    private function updatePaymentStatus($paymentId, $status, $gatewayData = null)
    {
        try {
            $payment = Payment::where('payment_id', $paymentId)->first();
            
            if ($payment) {
                $updateData = ['status' => $status];
                
                // تحديث تاريخ الدفع عند إكمال الدفع
                if ($status === 'completed') {
                    $updateData['paid_at'] = now();
                }
                
                if ($gatewayData) {
                    $updateData['gateway_data'] = array_merge($payment->gateway_data ?? [], $gatewayData);
                }
                
                $payment->update($updateData);
                
                Log::info('Payment status updated successfully', [
                    'payment_id' => $paymentId,
                    'status' => $status,
                    'paid_at' => $updateData['paid_at'] ?? null
                ]);
            } else {
                Log::error('Payment not found for status update', ['payment_id' => $paymentId]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to update payment status: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
                'status' => $status
            ]);
        }
    }
    
    private function getPayPalAccessToken()
    {
        try {
            Log::info('PayPal Access Token Request', [
                'client_id' => $this->paypalClientId ? 'SET' : 'NOT SET',
                'secret' => $this->paypalSecret ? 'SET' : 'NOT SET',
                'base_url' => $this->paypalBaseUrl
            ]);
            
            $response = Http::withBasicAuth($this->paypalClientId, $this->paypalSecret)
                ->asForm()
                ->post($this->paypalBaseUrl . '/v1/oauth2/token', [
                    'grant_type' => 'client_credentials'
                ]);
            
            Log::info('PayPal Access Token Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            if ($response->successful()) {
                Log::info('PayPal Access Token Retrieved Successfully');
                return $response->json()['access_token'];
            }
            
            Log::error('PayPal Access Token Failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('PayPal Access Token Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function createPayPalOrder($accessToken, $amount, $user, $planId)
    {
        try {
            $response = Http::withToken($accessToken)
                ->post($this->paypalBaseUrl . '/v2/checkout/orders', [
                    'intent' => 'CAPTURE',
                    'purchase_units' => [[
                        'amount' => [
                            'currency_code' => 'USD',
                            'value' => number_format($amount, 2, '.', '')
                        ],
                        'description' => 'اشتراك في الباقة رقم ' . $planId
                    ]],
                    'application_context' => [
                        'return_url' => route('dashboard'),
                        'cancel_url' => route('dashboard'),
                        'brand_name' => config('app.name'),
                        'user_action' => 'PAY_NOW'
                    ]
                ]);
            
            Log::info('PayPal API Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('PayPal API Error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('PayPal Create Order Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function getPayPalOrderDetails($accessToken, $orderId)
    {
        try {
            $response = Http::withToken($accessToken)
                ->get($this->paypalBaseUrl . '/v2/checkout/orders/' . $orderId);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('PayPal Get Order Details Failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('PayPal Get Order Details Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function capturePayPalOrder($accessToken, $orderId)
    {
        try {
            Log::info('PayPal Capture Order Started', [
                'order_id' => $orderId,
                'base_url' => $this->paypalBaseUrl
            ]);
            
            $response = Http::withToken($accessToken)
                ->asJson()
                ->post($this->paypalBaseUrl . '/v2/checkout/orders/' . $orderId . '/capture', (object)[]);
            
            Log::info('PayPal Capture API Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            if ($response->successful()) {
                $result = $response->json();
                Log::info('PayPal Capture Successful', ['result' => $result]);
                return $result;
            }
            
            Log::error('PayPal Capture Failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('PayPal Capture Order Error: ' . $e->getMessage());
            return null;
        }
    }
}