<?php

namespace App\Services;

use App\Helpers\ServiceResult;
use App\Helpers\StatusCodes;
use App\Models\Profile;
use App\Models\User;
use App\Traits\LogsTrait;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ProfileService extends BaseService
{
    use AuthorizesRequests, LogsTrait;

    public function allProfiles(int $itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $query = Profile::with(['user', 'theme']);

        if ($trashed) {
            $query->onlyTrashed();
        }

        if (auth()->user()->role_id != 1) {
            $query->where('user_id', auth()->user()->id);
        }

        $profiles = $query->orderBy($orderBy, $sortBy)->paginate($itemsPerPage);

        if ($profiles->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $profiles;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function singleProfile(int $profileId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $profile = Profile::with([
            'user', 'theme', 'skills', 'education', 'experiences', 
            'categories', 'portfolios', 'socials'
        ])->find($profileId);

        if ($profile) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function createProfile(array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::OPERATION_FAILED;

        $profile = Profile::create($data);
        
        if ($profile) {
            // تسجيل استخدام الميزة
            $user = User::find($data['user_id']);
            if ($user) {
                $user->recordFeatureUsage('personal-profile', 1);
            }
            
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function updateProfile(int $profileId, array $profileData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $profile = Profile::findOrFail($profileId);
        $this->authorize('update', $profile);

        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        $profile->fill($profileData);

        if ($profile->save()) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function deleteProfile(int $profileId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $profile = Profile::findOrFail($profileId);

        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        if ($profile->delete()) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function getProfileBySlug(string $slug): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;

        $profile = Profile::with([
            'user', 'theme', 'skills', 'education', 'experiences',
            'categories.portfolio', 'portfolios', 'socials'
        ])->where('slug', $slug)
          ->where('is_active', true)
          ->where('visibility', 'public')
          ->first();

        if ($profile) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function searchProfiles(string $term, int $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $query = Profile::with(['user', 'theme'])
            ->where(function ($query) use ($term) {
                $query->where('name', 'like', "%$term%")
                      ->orWhere('bio', 'like', "%$term%")
                      ->orWhere('job_title', 'like', "%$term%");
            });

        if (auth()->user()->role_id != 1) {
            $query->where('user_id', auth()->user()->id);
        }

        $profiles = $query->paginate($itemsPerPage);

        if ($profiles->count() > 0) {
            $serviceResult->Status = true;
            $serviceResult->data = $profiles;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function attachSkills(int $profileId, array $skillsData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $profile = Profile::find($profileId);
        
        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        foreach ($skillsData as $skillData) {
            $profile->skills()->create(array_merge($skillData, [
                'user_id' => $profile->user_id,
                'profile_id' => $profileId
            ]));
        }

        $serviceResult->Status = true;
        $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        return $serviceResult;
    }

    public function attachEducation(int $profileId, array $educationData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $profile = Profile::find($profileId);
        
        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        foreach ($educationData as $eduData) {
            $profile->education()->create(array_merge($eduData, [
                'user_id' => $profile->user_id,
                'profile_id' => $profileId
            ]));
        }

        $serviceResult->Status = true;
        $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        return $serviceResult;
    }

    public function attachExperiences(int $profileId, array $experiencesData): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $profile = Profile::find($profileId);
        
        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        foreach ($experiencesData as $expData) {
            $profile->experiences()->create(array_merge($expData, [
                'user_id' => $profile->user_id,
                'profile_id' => $profileId
            ]));
        }

        $serviceResult->Status = true;
        $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        return $serviceResult;
    }

    public function restoreProfile(int $profileId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $profile = Profile::withTrashed()->find($profileId);

        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        if ($profile->restore()) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function forceDeleteProfile(int $profileId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->Status = false;
        $serviceResult->data = [];
        $serviceResult->Code = StatusCodes::AUTHENTICATION_FAILED;

        $profile = Profile::withTrashed()->find($profileId);

        if (!$profile) {
            $serviceResult->Code = StatusCodes::DATA_NOT_FOUND;
            return $serviceResult;
        }

        if ($profile->forceDelete()) {
            $serviceResult->Status = true;
            $serviceResult->data = $profile;
            $serviceResult->Code = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->Code = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }
}