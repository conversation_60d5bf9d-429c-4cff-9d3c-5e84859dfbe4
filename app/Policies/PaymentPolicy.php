<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;

class PaymentPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->hasPermission('payments.view');
    }

    public function view(User $user, Payment $payment = null): bool
    {
        if ($user->hasPermission('payments.view')) {
            return true;
        }
        
        return $payment && $payment->user_id === $user->id;
    }

    public function create(User $user): bool
    {
        return $user->hasPermission('payments.create');
    }

    public function update(User $user, Payment $payment): bool
    {
        return $user->hasPermission('payments.update');
    }

    public function delete(User $user, Payment $payment): bool
    {
        return $user->hasPermission('payments.delete');
    }
}