<?php

namespace App\Policies;

use App\Models\Profile;
use App\Models\User;

class ProfilePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->hasPermission('browse_profiles');
    }

    public function view(User $user, Profile $profile): bool
    {
        return $user->hasPermission('read_profiles') || $user->id === $profile->user_id;
    }

    public function create(User $user): bool
    {
        return $user->hasPermission('add_profiles');
    }

    public function update(User $user, Profile $profile): bool
    {
        return $user->hasPermission('edit_profiles') || $user->id === $profile->user_id;
    }

    public function delete(User $user, Profile $profile): bool
    {
        return $user->hasPermission('delete_profiles') || $user->id === $profile->user_id;
    }

    public function restore(User $user, Profile $profile): bool
    {
        return $user->hasPermission('restore_profiles');
    }

    public function forceDelete(User $user, Profile $profile): bool
    {
        return $user->hasPermission('forceDelete_profiles');
    }
}